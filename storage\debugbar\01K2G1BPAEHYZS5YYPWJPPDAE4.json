{"__meta": {"id": "01K2G1BPAEHYZS5YYPWJPPDAE4", "datetime": "2025-08-12 21:00:43", "utime": **********.214986, "method": "GET", "uri": "/en/rent-properties", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755032440.5901, "end": **********.215001, "duration": 2.624901056289673, "duration_str": "2.62s", "measures": [{"label": "Booting", "start": 1755032440.5901, "relative_start": 0, "end": **********.437151, "relative_end": **********.437151, "duration": 0.****************, "duration_str": "847ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.437162, "relative_start": 0.****************, "end": **********.215003, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.455925, "relative_start": 0.****************, "end": **********.492703, "relative_end": **********.492703, "duration": 0.*****************, "duration_str": "36.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.xmetr::views.page", "start": **********.713358, "relative_start": 1.****************, "end": **********.713358, "relative_end": **********.713358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties", "start": **********.796677, "relative_start": 1.****************, "end": **********.796677, "relative_end": **********.796677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.listing", "start": **********.80675, "relative_start": 1.2166500091552734, "end": **********.80675, "relative_end": **********.80675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.listing-layouts.xmetr", "start": **********.807477, "relative_start": 1.217376947402954, "end": **********.807477, "relative_end": **********.807477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.map", "start": **********.955084, "relative_start": 1.3649840354919434, "end": **********.955084, "relative_end": **********.955084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.index", "start": **********.956857, "relative_start": 1.3667569160461426, "end": **********.956857, "relative_end": **********.956857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.grid", "start": **********.95775, "relative_start": 1.367650032043457, "end": **********.95775, "relative_end": **********.95775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.958584, "relative_start": 1.3684840202331543, "end": **********.958584, "relative_end": **********.958584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.020142, "relative_start": 1.430042028427124, "end": **********.020142, "relative_end": **********.020142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.062418, "relative_start": 1.4723179340362549, "end": **********.062418, "relative_end": **********.062418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.111303, "relative_start": 1.5212030410766602, "end": **********.111303, "relative_end": **********.111303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.157801, "relative_start": 1.5677008628845215, "end": **********.157801, "relative_end": **********.157801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.198236, "relative_start": 1.6081359386444092, "end": **********.198236, "relative_end": **********.198236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.23753, "relative_start": 1.6474299430847168, "end": **********.23753, "relative_end": **********.23753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.272297, "relative_start": 1.682196855545044, "end": **********.272297, "relative_end": **********.272297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.325033, "relative_start": 1.7349328994750977, "end": **********.325033, "relative_end": **********.325033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.370111, "relative_start": 1.7800109386444092, "end": **********.370111, "relative_end": **********.370111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.411263, "relative_start": 1.8211629390716553, "end": **********.411263, "relative_end": **********.411263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.449953, "relative_start": 1.8598530292510986, "end": **********.449953, "relative_end": **********.449953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.487826, "relative_start": 1.897726058959961, "end": **********.487826, "relative_end": **********.487826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.519148, "relative_start": 1.9290480613708496, "end": **********.519148, "relative_end": **********.519148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.551914, "relative_start": 1.9618139266967773, "end": **********.551914, "relative_end": **********.551914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.584094, "relative_start": 1.9939939975738525, "end": **********.584094, "relative_end": **********.584094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.621251, "relative_start": 2.031151056289673, "end": **********.621251, "relative_end": **********.621251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.674214, "relative_start": 2.084113836288452, "end": **********.674214, "relative_end": **********.674214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.704308, "relative_start": 2.1142079830169678, "end": **********.704308, "relative_end": **********.704308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.743624, "relative_start": 2.1535239219665527, "end": **********.743624, "relative_end": **********.743624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.772823, "relative_start": 2.182723045349121, "end": **********.772823, "relative_end": **********.772823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.813254, "relative_start": 2.223154067993164, "end": **********.813254, "relative_end": **********.813254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.854532, "relative_start": 2.264431953430176, "end": **********.854532, "relative_end": **********.854532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.907172, "relative_start": 2.3170719146728516, "end": **********.907172, "relative_end": **********.907172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.pagination", "start": **********.941072, "relative_start": 2.3509719371795654, "end": **********.941072, "relative_end": **********.941072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98abfcd8227e681cb9ad72e7d1720a64", "start": **********.947239, "relative_start": 2.3571388721466064, "end": **********.947239, "relative_end": **********.947239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.property-map-content", "start": **********.95451, "relative_start": 2.364409923553467, "end": **********.95451, "relative_end": **********.95451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.full-width", "start": **********.994893, "relative_start": 2.4047930240631104, "end": **********.994893, "relative_end": **********.994893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.998824, "relative_start": 2.408723831176758, "end": **********.998824, "relative_end": **********.998824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.header", "start": **********.999589, "relative_start": 2.4094889163970947, "end": **********.999589, "relative_end": **********.999589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.008085, "relative_start": 2.417984962463379, "end": **********.008085, "relative_end": **********.008085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.010185, "relative_start": 2.4200849533081055, "end": **********.010185, "relative_end": **********.010185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.057768, "relative_start": 2.467668056488037, "end": **********.057768, "relative_end": **********.057768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.058499, "relative_start": 2.4683990478515625, "end": **********.058499, "relative_end": **********.058499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.069842, "relative_start": 2.4797420501708984, "end": **********.069842, "relative_end": **********.069842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": **********.071456, "relative_start": 2.481355905532837, "end": **********.071456, "relative_end": **********.071456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": **********.07365, "relative_start": 2.4835498332977295, "end": **********.07365, "relative_end": **********.07365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.075227, "relative_start": 2.4851269721984863, "end": **********.075227, "relative_end": **********.075227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": **********.07995, "relative_start": 2.4898500442504883, "end": **********.07995, "relative_end": **********.07995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": **********.080394, "relative_start": 2.4902939796447754, "end": **********.080394, "relative_end": **********.080394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.080762, "relative_start": 2.490661859512329, "end": **********.080762, "relative_end": **********.080762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a7781df99689693edd2c6c189d767089", "start": **********.081607, "relative_start": 2.491507053375244, "end": **********.081607, "relative_end": **********.081607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": **********.082237, "relative_start": 2.4921369552612305, "end": **********.082237, "relative_end": **********.082237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": **********.082616, "relative_start": 2.492516040802002, "end": **********.082616, "relative_end": **********.082616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": **********.083402, "relative_start": 2.4933018684387207, "end": **********.083402, "relative_end": **********.083402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.084565, "relative_start": 2.494464874267578, "end": **********.084565, "relative_end": **********.084565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.modal-authentication", "start": **********.086724, "relative_start": 2.496623992919922, "end": **********.086724, "relative_end": **********.086724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.095531, "relative_start": 2.5054309368133545, "end": **********.095531, "relative_end": **********.095531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.099524, "relative_start": 2.5094239711761475, "end": **********.099524, "relative_end": **********.099524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.100241, "relative_start": 2.510140895843506, "end": **********.100241, "relative_end": **********.100241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.101111, "relative_start": 2.5110108852386475, "end": **********.101111, "relative_end": **********.101111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.10186, "relative_start": 2.5117599964141846, "end": **********.10186, "relative_end": **********.10186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.102307, "relative_start": 2.51220703125, "end": **********.102307, "relative_end": **********.102307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.102802, "relative_start": 2.512701988220215, "end": **********.102802, "relative_end": **********.102802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.103363, "relative_start": 2.513262987136841, "end": **********.103363, "relative_end": **********.103363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.103921, "relative_start": 2.5138208866119385, "end": **********.103921, "relative_end": **********.103921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.104291, "relative_start": 2.514190912246704, "end": **********.104291, "relative_end": **********.104291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.104725, "relative_start": 2.514624834060669, "end": **********.104725, "relative_end": **********.104725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.104955, "relative_start": 2.514854907989502, "end": **********.104955, "relative_end": **********.104955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.105252, "relative_start": 2.5151519775390625, "end": **********.105252, "relative_end": **********.105252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": **********.105681, "relative_start": 2.515580892562866, "end": **********.105681, "relative_end": **********.105681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.106125, "relative_start": 2.5160250663757324, "end": **********.106125, "relative_end": **********.106125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.106408, "relative_start": 2.516308069229126, "end": **********.106408, "relative_end": **********.106408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.106866, "relative_start": 2.516765832901001, "end": **********.106866, "relative_end": **********.106866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.107085, "relative_start": 2.5169849395751953, "end": **********.107085, "relative_end": **********.107085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.107359, "relative_start": 2.517258882522583, "end": **********.107359, "relative_end": **********.107359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.107663, "relative_start": 2.5175628662109375, "end": **********.107663, "relative_end": **********.107663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.107984, "relative_start": 2.5178840160369873, "end": **********.107984, "relative_end": **********.107984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.108413, "relative_start": 2.518312931060791, "end": **********.108413, "relative_end": **********.108413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.108637, "relative_start": 2.5185370445251465, "end": **********.108637, "relative_end": **********.108637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.108916, "relative_start": 2.5188159942626953, "end": **********.108916, "relative_end": **********.108916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": **********.109344, "relative_start": 2.5192439556121826, "end": **********.109344, "relative_end": **********.109344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": **********.110037, "relative_start": 2.519937038421631, "end": **********.110037, "relative_end": **********.110037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": **********.110648, "relative_start": 2.520547866821289, "end": **********.110648, "relative_end": **********.110648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.111229, "relative_start": 2.5211288928985596, "end": **********.111229, "relative_end": **********.111229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.112263, "relative_start": 2.522162914276123, "end": **********.112263, "relative_end": **********.112263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.112769, "relative_start": 2.5226688385009766, "end": **********.112769, "relative_end": **********.112769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.113068, "relative_start": 2.522968053817749, "end": **********.113068, "relative_end": **********.113068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.113384, "relative_start": 2.5232839584350586, "end": **********.113384, "relative_end": **********.113384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.113716, "relative_start": 2.523615837097168, "end": **********.113716, "relative_end": **********.113716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.11407, "relative_start": 2.523969888687134, "end": **********.11407, "relative_end": **********.11407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.114509, "relative_start": 2.524409055709839, "end": **********.114509, "relative_end": **********.114509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.114743, "relative_start": 2.5246429443359375, "end": **********.114743, "relative_end": **********.114743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.115018, "relative_start": 2.5249178409576416, "end": **********.115018, "relative_end": **********.115018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.11532, "relative_start": 2.5252199172973633, "end": **********.11532, "relative_end": **********.11532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.115645, "relative_start": 2.5255448818206787, "end": **********.115645, "relative_end": **********.115645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.116073, "relative_start": 2.525972843170166, "end": **********.116073, "relative_end": **********.116073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.116306, "relative_start": 2.5262060165405273, "end": **********.116306, "relative_end": **********.116306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.116578, "relative_start": 2.5264780521392822, "end": **********.116578, "relative_end": **********.116578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.116881, "relative_start": 2.526780843734741, "end": **********.116881, "relative_end": **********.116881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.117226, "relative_start": 2.527125835418701, "end": **********.117226, "relative_end": **********.117226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.117654, "relative_start": 2.5275540351867676, "end": **********.117654, "relative_end": **********.117654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.117884, "relative_start": 2.5277838706970215, "end": **********.117884, "relative_end": **********.117884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.118164, "relative_start": 2.528064012527466, "end": **********.118164, "relative_end": **********.118164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.118475, "relative_start": 2.5283749103546143, "end": **********.118475, "relative_end": **********.118475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.118801, "relative_start": 2.528701066970825, "end": **********.118801, "relative_end": **********.118801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.119248, "relative_start": 2.5291478633880615, "end": **********.119248, "relative_end": **********.119248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.119477, "relative_start": 2.529376983642578, "end": **********.119477, "relative_end": **********.119477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.11973, "relative_start": 2.529629945755005, "end": **********.11973, "relative_end": **********.11973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.120295, "relative_start": 2.5301949977874756, "end": **********.120295, "relative_end": **********.120295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.120822, "relative_start": 2.53072190284729, "end": **********.120822, "relative_end": **********.120822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.121129, "relative_start": 2.531028985977173, "end": **********.121129, "relative_end": **********.121129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.121449, "relative_start": 2.531348943710327, "end": **********.121449, "relative_end": **********.121449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.121875, "relative_start": 2.5317749977111816, "end": **********.121875, "relative_end": **********.121875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.122095, "relative_start": 2.5319950580596924, "end": **********.122095, "relative_end": **********.122095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.122359, "relative_start": 2.532258987426758, "end": **********.122359, "relative_end": **********.122359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.12265, "relative_start": 2.5325498580932617, "end": **********.12265, "relative_end": **********.12265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.122947, "relative_start": 2.5328469276428223, "end": **********.122947, "relative_end": **********.122947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.123354, "relative_start": 2.5332539081573486, "end": **********.123354, "relative_end": **********.123354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.123551, "relative_start": 2.5334508419036865, "end": **********.123551, "relative_end": **********.123551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.123795, "relative_start": 2.5336949825286865, "end": **********.123795, "relative_end": **********.123795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.124068, "relative_start": 2.533967971801758, "end": **********.124068, "relative_end": **********.124068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.124366, "relative_start": 2.5342659950256348, "end": **********.124366, "relative_end": **********.124366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.124773, "relative_start": 2.534672975540161, "end": **********.124773, "relative_end": **********.124773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.12497, "relative_start": 2.534869909286499, "end": **********.12497, "relative_end": **********.12497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.12522, "relative_start": 2.5351200103759766, "end": **********.12522, "relative_end": **********.12522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.125496, "relative_start": 2.535395860671997, "end": **********.125496, "relative_end": **********.125496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.125795, "relative_start": 2.5356948375701904, "end": **********.125795, "relative_end": **********.125795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.1262, "relative_start": 2.536099910736084, "end": **********.1262, "relative_end": **********.1262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.126399, "relative_start": 2.536298990249634, "end": **********.126399, "relative_end": **********.126399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.126621, "relative_start": 2.5365209579467773, "end": **********.126621, "relative_end": **********.126621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.128108, "relative_start": 2.538007974624634, "end": **********.128108, "relative_end": **********.128108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.133754, "relative_start": 2.543653964996338, "end": **********.133754, "relative_end": **********.133754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::forms.auth", "start": **********.135053, "relative_start": 2.544952869415283, "end": **********.135053, "relative_end": **********.135053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "start": **********.136371, "relative_start": 2.5462708473205566, "end": **********.136371, "relative_end": **********.136371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": **********.136853, "relative_start": 2.5467529296875, "end": **********.136853, "relative_end": **********.136853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.137285, "relative_start": 2.547184944152832, "end": **********.137285, "relative_end": **********.137285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.137633, "relative_start": 2.5475330352783203, "end": **********.137633, "relative_end": **********.137633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.138092, "relative_start": 2.547991991043091, "end": **********.138092, "relative_end": **********.138092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.138332, "relative_start": 2.548231840133667, "end": **********.138332, "relative_end": **********.138332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.138591, "relative_start": 2.5484910011291504, "end": **********.138591, "relative_end": **********.138591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.139017, "relative_start": 2.548917055130005, "end": **********.139017, "relative_end": **********.139017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.139461, "relative_start": 2.549360990524292, "end": **********.139461, "relative_end": **********.139461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.13975, "relative_start": 2.549649953842163, "end": **********.13975, "relative_end": **********.13975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.140185, "relative_start": 2.5500850677490234, "end": **********.140185, "relative_end": **********.140185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.140417, "relative_start": 2.5503170490264893, "end": **********.140417, "relative_end": **********.140417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.1407, "relative_start": 2.550600051879883, "end": **********.1407, "relative_end": **********.1407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.141019, "relative_start": 2.5509190559387207, "end": **********.141019, "relative_end": **********.141019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.141372, "relative_start": 2.551271915435791, "end": **********.141372, "relative_end": **********.141372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.141649, "relative_start": 2.551548957824707, "end": **********.141649, "relative_end": **********.141649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.142083, "relative_start": 2.551982879638672, "end": **********.142083, "relative_end": **********.142083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.142331, "relative_start": 2.5522308349609375, "end": **********.142331, "relative_end": **********.142331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.14261, "relative_start": 2.5525100231170654, "end": **********.14261, "relative_end": **********.14261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": **********.142929, "relative_start": 2.5528290271759033, "end": **********.142929, "relative_end": **********.142929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.143288, "relative_start": 2.553187847137451, "end": **********.143288, "relative_end": **********.143288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.143548, "relative_start": 2.553447961807251, "end": **********.143548, "relative_end": **********.143548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.143977, "relative_start": 2.5538768768310547, "end": **********.143977, "relative_end": **********.143977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.144252, "relative_start": 2.554152011871338, "end": **********.144252, "relative_end": **********.144252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.14467, "relative_start": 2.554569959640503, "end": **********.14467, "relative_end": **********.14467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.145006, "relative_start": 2.554905891418457, "end": **********.145006, "relative_end": **********.145006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.145396, "relative_start": 2.555295944213867, "end": **********.145396, "relative_end": **********.145396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.145885, "relative_start": 2.5557849407196045, "end": **********.145885, "relative_end": **********.145885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.146153, "relative_start": 2.5560529232025146, "end": **********.146153, "relative_end": **********.146153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.146669, "relative_start": 2.5565688610076904, "end": **********.146669, "relative_end": **********.146669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.146994, "relative_start": 2.556894063949585, "end": **********.146994, "relative_end": **********.146994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.147355, "relative_start": 2.5572550296783447, "end": **********.147355, "relative_end": **********.147355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.147779, "relative_start": 2.5576789379119873, "end": **********.147779, "relative_end": **********.147779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.148008, "relative_start": 2.557908058166504, "end": **********.148008, "relative_end": **********.148008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.148256, "relative_start": 2.5581560134887695, "end": **********.148256, "relative_end": **********.148256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.148556, "relative_start": 2.5584559440612793, "end": **********.148556, "relative_end": **********.148556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.148964, "relative_start": 2.558863878250122, "end": **********.148964, "relative_end": **********.148964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.149267, "relative_start": 2.55916690826416, "end": **********.149267, "relative_end": **********.149267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.149571, "relative_start": 2.5594708919525146, "end": **********.149571, "relative_end": **********.149571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.149987, "relative_start": 2.559886932373047, "end": **********.149987, "relative_end": **********.149987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.150197, "relative_start": 2.5600969791412354, "end": **********.150197, "relative_end": **********.150197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.150447, "relative_start": 2.560346841812134, "end": **********.150447, "relative_end": **********.150447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.150725, "relative_start": 2.560624837875366, "end": **********.150725, "relative_end": **********.150725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.151029, "relative_start": 2.5609290599823, "end": **********.151029, "relative_end": **********.151029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.151445, "relative_start": 2.561344861984253, "end": **********.151445, "relative_end": **********.151445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.151659, "relative_start": 2.561558961868286, "end": **********.151659, "relative_end": **********.151659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.15191, "relative_start": 2.56181001663208, "end": **********.15191, "relative_end": **********.15191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.152233, "relative_start": 2.5621328353881836, "end": **********.152233, "relative_end": **********.152233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.152538, "relative_start": 2.5624380111694336, "end": **********.152538, "relative_end": **********.152538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.152944, "relative_start": 2.5628440380096436, "end": **********.152944, "relative_end": **********.152944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.153163, "relative_start": 2.563062906265259, "end": **********.153163, "relative_end": **********.153163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.153432, "relative_start": 2.5633318424224854, "end": **********.153432, "relative_end": **********.153432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.153714, "relative_start": 2.5636138916015625, "end": **********.153714, "relative_end": **********.153714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.154021, "relative_start": 2.5639209747314453, "end": **********.154021, "relative_end": **********.154021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.15443, "relative_start": 2.5643298625946045, "end": **********.15443, "relative_end": **********.15443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.154642, "relative_start": 2.564542055130005, "end": **********.154642, "relative_end": **********.154642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.154876, "relative_start": 2.5647759437561035, "end": **********.154876, "relative_end": **********.154876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.15735, "relative_start": 2.5672500133514404, "end": **********.15735, "relative_end": **********.15735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.footer", "start": **********.158847, "relative_start": 2.568747043609619, "end": **********.158847, "relative_end": **********.158847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.161679, "relative_start": 2.5715789794921875, "end": **********.161679, "relative_end": **********.161679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.base", "start": **********.162508, "relative_start": 2.5724079608917236, "end": **********.162508, "relative_end": **********.162508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.166563, "relative_start": 2.576462984085083, "end": **********.166563, "relative_end": **********.166563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.hreflang", "start": **********.191947, "relative_start": 2.601846933364868, "end": **********.191947, "relative_end": **********.191947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.hreflang", "start": **********.197001, "relative_start": 2.606900930404663, "end": **********.197001, "relative_end": **********.197001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.199971, "relative_start": 2.6098709106445312, "end": **********.199971, "relative_end": **********.199971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.201373, "relative_start": 2.6112730503082275, "end": **********.201373, "relative_end": **********.201373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.212079, "relative_start": 2.621978998184204, "end": **********.212307, "relative_end": **********.212307, "duration": 0.00022792816162109375, "duration_str": "228μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 71063400, "peak_usage_str": "68MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 197, "nb_templates": 197, "templates": [{"name": "1x theme.xmetr::views.page", "param_count": null, "params": [], "start": **********.713327, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/page.blade.phptheme.xmetr::views.page", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Fpage.blade.php:1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.page"}, {"name": "1x theme.xmetr::views.real-estate.properties", "param_count": null, "params": [], "start": **********.796649, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties.blade.phptheme.xmetr::views.real-estate.properties", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties.blade.php:1", "ajax": false, "filename": "properties.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.properties"}, {"name": "1x theme.xmetr::views.real-estate.partials.listing", "param_count": null, "params": [], "start": **********.806723, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/listing.blade.phptheme.xmetr::views.real-estate.partials.listing", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Flisting.blade.php:1", "ajax": false, "filename": "listing.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.listing"}, {"name": "1x theme.xmetr::views.real-estate.listing-layouts.xmetr", "param_count": null, "params": [], "start": **********.807454, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/listing-layouts/xmetr.blade.phptheme.xmetr::views.real-estate.listing-layouts.xmetr", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Flisting-layouts%2Fxmetr.blade.php:1", "ajax": false, "filename": "xmetr.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.listing-layouts.xmetr"}, {"name": "1x theme.xmetr::views.real-estate.partials.map", "param_count": null, "params": [], "start": **********.955059, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/map.blade.phptheme.xmetr::views.real-estate.partials.map", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Fmap.blade.php:1", "ajax": false, "filename": "map.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.map"}, {"name": "1x theme.xmetr::views.real-estate.properties.index", "param_count": null, "params": [], "start": **********.956829, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/index.blade.phptheme.xmetr::views.real-estate.properties.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.properties.index"}, {"name": "1x theme.xmetr::views.real-estate.properties.grid", "param_count": null, "params": [], "start": **********.957725, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/grid.blade.phptheme.xmetr::views.real-estate.properties.grid", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Fgrid.blade.php:1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.properties.grid"}, {"name": "24x theme.xmetr::views.real-estate.properties.item-grid", "param_count": null, "params": [], "start": **********.95855, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.phptheme.xmetr::views.real-estate.properties.item-grid", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Fitem-grid.blade.php:1", "ajax": false, "filename": "item-grid.blade.php", "line": "?"}, "render_count": 24, "name_original": "theme.xmetr::views.real-estate.properties.item-grid"}, {"name": "1x theme.xmetr::partials.pagination", "param_count": null, "params": [], "start": **********.941046, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/pagination.blade.phptheme.xmetr::partials.pagination", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fpagination.blade.php:1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.pagination"}, {"name": "1x __components::98abfcd8227e681cb9ad72e7d1720a64", "param_count": null, "params": [], "start": **********.947214, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98abfcd8227e681cb9ad72e7d1720a64.blade.php__components::98abfcd8227e681cb9ad72e7d1720a64", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98abfcd8227e681cb9ad72e7d1720a64.blade.php:1", "ajax": false, "filename": "98abfcd8227e681cb9ad72e7d1720a64.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::98abfcd8227e681cb9ad72e7d1720a64"}, {"name": "1x theme.xmetr::views.real-estate.partials.property-map-content", "param_count": null, "params": [], "start": **********.954487, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/property-map-content.blade.phptheme.xmetr::views.real-estate.partials.property-map-content", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Fproperty-map-content.blade.php:1", "ajax": false, "filename": "property-map-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.property-map-content"}, {"name": "1x theme.xmetr::layouts.full-width", "param_count": null, "params": [], "start": **********.994867, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/full-width.blade.phptheme.xmetr::layouts.full-width", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Ffull-width.blade.php:1", "ajax": false, "filename": "full-width.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.full-width"}, {"name": "4x plugins/ads::partials.ad-display", "param_count": null, "params": [], "start": **********.998799, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/ads/resources/views/partials/ad-display.blade.phpplugins/ads::partials.ad-display", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fads%2Fresources%2Fviews%2Fpartials%2Fad-display.blade.php:1", "ajax": false, "filename": "ad-display.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/ads::partials.ad-display"}, {"name": "1x theme.xmetr::partials.header", "param_count": null, "params": [], "start": **********.999568, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.phptheme.xmetr::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.header"}, {"name": "4x plugins/social-login::login-options", "param_count": null, "params": [], "start": **********.008062, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/social-login/resources/views/login-options.blade.phpplugins/social-login::login-options", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fsocial-login%2Fresources%2Fviews%2Flogin-options.blade.php:1", "ajax": false, "filename": "login-options.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/social-login::login-options"}, {"name": "2x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.069805, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php:1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "3x __components::054b349d8fa17e9f8644145c2fbc1063", "param_count": null, "params": [], "start": **********.071433, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/054b349d8fa17e9f8644145c2fbc1063.blade.php__components::054b349d8fa17e9f8644145c2fbc1063", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F054b349d8fa17e9f8644145c2fbc1063.blade.php:1", "ajax": false, "filename": "054b349d8fa17e9f8644145c2fbc1063.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::054b349d8fa17e9f8644145c2fbc1063"}, {"name": "2x __components::6cb431528960518c12f7bad152d7b46d", "param_count": null, "params": [], "start": **********.073628, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6cb431528960518c12f7bad152d7b46d.blade.php__components::6cb431528960518c12f7bad152d7b46d", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6cb431528960518c12f7bad152d7b46d.blade.php:1", "ajax": false, "filename": "6cb431528960518c12f7bad152d7b46d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6cb431528960518c12f7bad152d7b46d"}, {"name": "2x __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "param_count": null, "params": [], "start": **********.079926, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php__components::f6b8d5c7f3ac3703ebfdef4449133ff4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff6b8d5c7f3ac3703ebfdef4449133ff4.blade.php:1", "ajax": false, "filename": "f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f6b8d5c7f3ac3703ebfdef4449133ff4"}, {"name": "1x __components::a7781df99689693edd2c6c189d767089", "param_count": null, "params": [], "start": **********.081586, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a7781df99689693edd2c6c189d767089.blade.php__components::a7781df99689693edd2c6c189d767089", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa7781df99689693edd2c6c189d767089.blade.php:1", "ajax": false, "filename": "a7781df99689693edd2c6c189d767089.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a7781df99689693edd2c6c189d767089"}, {"name": "1x theme.xmetr::partials.modal-authentication", "param_count": null, "params": [], "start": **********.086703, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/modal-authentication.blade.phptheme.xmetr::partials.modal-authentication", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fmodal-authentication.blade.php:1", "ajax": false, "filename": "modal-authentication.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.modal-authentication"}, {"name": "4x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.095506, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php:1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.099499, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php:1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "17x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.10022, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php:1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 17, "name_original": "core/base::forms.fields.html"}, {"name": "23x ********************************::form.field", "param_count": null, "params": [], "start": **********.101089, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php:1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 23, "name_original": "********************************::form.field"}, {"name": "23x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.101838, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php:1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.help-block"}, {"name": "23x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.102287, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php:1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.errors"}, {"name": "25x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.102781, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php:1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 25, "name_original": "core/base::forms.columns.column-span"}, {"name": "2x core/base::forms.fields.email", "param_count": null, "params": [], "start": **********.103342, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php:1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.email"}, {"name": "5x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.1039, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.partials.label"}, {"name": "2x core/base::forms.fields.password", "param_count": null, "params": [], "start": **********.105659, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/password.blade.phpcore/base::forms.fields.password", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fpassword.blade.php:1", "ajax": false, "filename": "password.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.password"}, {"name": "1x core/base::forms.fields.on-off-checkbox", "param_count": null, "params": [], "start": **********.109322, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off-checkbox.blade.phpcore/base::forms.fields.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off-checkbox"}, {"name": "1x core/base::forms.partials.on-off-checkbox", "param_count": null, "params": [], "start": **********.110014, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off-checkbox.blade.phpcore/base::forms.partials.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off-checkbox"}, {"name": "1x ********************************::form.on-off.checkbox", "param_count": null, "params": [], "start": **********.110625, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php********************************::form.on-off.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": **********.11119, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "2x laravel-form-builder::button", "param_count": null, "params": [], "start": **********.120275, "type": "php", "hash": "phpD:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php:1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 2, "name_original": "laravel-form-builder::button"}, {"name": "1x plugins/real-estate::forms.auth", "param_count": null, "params": [], "start": **********.135031, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/forms/auth.blade.phpplugins/real-estate::forms.auth", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fforms%2Fauth.blade.php:1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::forms.auth"}, {"name": "1x __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "param_count": null, "params": [], "start": **********.13635, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php__components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php:1", "ajax": false, "filename": "2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2844accb98b3a6a2e1cb0b4f06fc3e6e"}, {"name": "1x core/base::forms.columns.form-open-wrapper", "param_count": null, "params": [], "start": **********.136832, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/form-open-wrapper.blade.phpcore/base::forms.columns.form-open-wrapper", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fform-open-wrapper.blade.php:1", "ajax": false, "filename": "form-open-wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.columns.form-open-wrapper"}, {"name": "1x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.138997, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php:1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.text"}, {"name": "1x theme.xmetr::partials.footer", "param_count": null, "params": [], "start": **********.158827, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/footer.blade.phptheme.xmetr::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.footer"}, {"name": "1x theme.xmetr::layouts.base", "param_count": null, "params": [], "start": **********.162482, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/base.blade.phptheme.xmetr::layouts.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.166541, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "2x plugins/language::partials.hreflang", "param_count": null, "params": [], "start": **********.191922, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/hreflang.blade.phpplugins/language::partials.hreflang", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fhreflang.blade.php:1", "ajax": false, "filename": "hreflang.blade.php", "line": "?"}, "render_count": 2, "name_original": "plugins/language::partials.hreflang"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.19995, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.201352, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php:1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 281, "nb_statements": 281, "nb_visible_statements": 281, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11369000000000004, "accumulated_duration_str": "114ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 181 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `slugs` where (`key` = 'rent-properties' and `prefix` = '') or exists (select * from `slugs_translations` where `slugs`.`id` = `slugs_translations`.`slugs_id` and (`key` = 'rent-properties' and `prefix` = '')) limit 1", "type": "query", "params": [], "bindings": ["rent-properties", "", "rent-properties", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5082572, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.721}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 30}], "start": **********.516621, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.721, "width_percent": 0.449}, {"sql": "select * from `pages` where (`id` = 14 and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": [14, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 18, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.522068, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.17, "width_percent": 0.616}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (14) and `slugs`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.526752, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.786, "width_percent": 0.581}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (14) and `meta_boxes`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/packages/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.552246, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.366, "width_percent": 0.554}, {"sql": "select count(*) as aggregate from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-08-12 21:00:41' or `never_expired` = 1) and `status` != 'rented'", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-08-12 21:00:41", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 261}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 665}, {"index": 24, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.739272, "duration": 0.00819, "duration_str": "8.19ms", "memory": 0, "memory_str": null, "filename": "RepositoriesAbstract.php:366", "source": {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsupport%2Fsrc%2FRepositories%2FEloquent%2FRepositoriesAbstract.php:366", "ajax": false, "filename": "RepositoriesAbstract.php", "line": "366"}, "connection": "xmetr", "explain": null, "start_percent": 2.92, "width_percent": 7.204}, {"sql": "select * from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-08-12 21:00:41' or `never_expired` = 1) and `status` != 'rented' order by `created_at` desc limit 24 offset 0", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-08-12 21:00:41", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 261}, {"index": 21, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 665}], "start": **********.749669, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 10.124, "width_percent": 2.797}, {"sql": "select `id`, `key`, `prefix`, `reference_id` from `slugs` where `slugs`.`reference_id` in (1206, 1207, 1208, 1209, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 261}], "start": **********.756685, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 12.921, "width_percent": 0.528}, {"sql": "select `id`, `name` from `states` where `states`.`id` in (192, 193, 197, 206)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 261}], "start": **********.764697, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 13.449, "width_percent": 0.396}, {"sql": "select `id`, `name` from `cities` where `cities`.`id` in (8189, 8204, 8205, 8206, 8211, 8212, 8216, 8297, 8316)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 261}], "start": **********.769051, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 13.845, "width_percent": 0.642}, {"sql": "select `id`, `is_default`, `exchange_rate`, `symbol`, `title`, `is_prefix_symbol` from `re_currencies` where `re_currencies`.`id` in (4, 10, 36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 261}], "start": **********.775733, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 14.487, "width_percent": 0.475}, {"sql": "select `re_categories`.`id`, `re_categories`.`name`, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` in (1206, 1207, 1208, 1209, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230) and `status` = 'published' order by `created_at` desc, `is_default` desc, `order` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 261}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 665}], "start": **********.787301, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 14.962, "width_percent": 0.792}, {"sql": "select `id`, `name` from `pages` where `status` = 'published' and `id` = '14' limit 1", "type": "query", "params": [], "bindings": ["published", "14"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 168}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 183}, {"index": 20, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties.blade.php", "line": 6}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.798301, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 15.753, "width_percent": 0.457}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (14) and `slugs`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 168}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 183}, {"index": 26, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties.blade.php", "line": 6}], "start": **********.800219, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.211, "width_percent": 0.325}, {"sql": "select * from `re_categories` where (`status` = 'published') order by `created_at` asc, `is_default` asc, `order` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 21}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8141842, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.536, "width_percent": 0.387}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (4, 7, 8) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Category'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Category"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 21}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.8158882, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.923, "width_percent": 0.246}, {"sql": "select * from `countries` where exists (select * from `re_properties` where `countries`.`id` = `re_properties`.`country_id`) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 141}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.823621, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.169, "width_percent": 0.396}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (22, 24, 27, 29, 30, 31, 32, 33, 34, 35, 37, 44, 49, 52, 110, 115, 149, 199) and `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\Country'", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\Country"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 141}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8269622, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.565, "width_percent": 0.387}, {"sql": "select * from `re_features` where exists (select * from `re_properties` inner join `re_property_features` on `re_properties`.`id` = `re_property_features`.`property_id` where `re_features`.`id` = `re_property_features`.`feature_id`) and `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": "view", "name": "theme.xmetr::views.real-estate.listing-layouts.xmetr", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/listing-layouts/xmetr.blade.php", "line": 338}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.881157, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.952, "width_percent": 0.528}, {"sql": "select * from `re_spoken_languages` where `status` = 'published' order by `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": "view", "name": "theme.xmetr::views.real-estate.listing-layouts.xmetr", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/listing-layouts/xmetr.blade.php", "line": 391}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.89688, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 18.48, "width_percent": 0.625}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.979514, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 19.105, "width_percent": 0.519}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.988389, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 19.624, "width_percent": 0.264}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1230", "type": "query", "params": [], "bindings": [1230], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.9916809, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 19.887, "width_percent": 0.537}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.994067, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 20.424, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.997489, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 20.741, "width_percent": 0.475}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.001547, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.216, "width_percent": 0.29}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1230 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1230], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0038722, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.506, "width_percent": 0.237}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.009156, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.743, "width_percent": 0.334}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0131571, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.078, "width_percent": 0.299}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.016706, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.377, "width_percent": 0.229}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.031009, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.605, "width_percent": 0.343}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1229", "type": "query", "params": [], "bindings": [1229], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.0342288, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 22.948, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0359478, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.177, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0384538, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.388, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0412269, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.599, "width_percent": 0.202}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.044858, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.802, "width_percent": 0.449}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1229 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1229], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.047763, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.25, "width_percent": 0.255}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.052347, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.505, "width_percent": 0.431}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.054261, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.936, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.058264, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.209, "width_percent": 0.396}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.072727, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.605, "width_percent": 0.413}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1228", "type": "query", "params": [], "bindings": [1228], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.076018, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 26.018, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 2 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.078049, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 26.291, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.081462, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 26.616, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.084478, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 26.95, "width_percent": 0.361}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.088264, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 27.311, "width_percent": 0.475}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.094728, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 27.786, "width_percent": 0.484}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1228 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1228], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.097936, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 28.27, "width_percent": 0.325}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.102349, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 28.595, "width_percent": 0.334}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.104283, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 28.93, "width_percent": 0.299}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.107943, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 29.229, "width_percent": 0.264}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.122616, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 29.492, "width_percent": 0.343}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1227", "type": "query", "params": [], "bindings": [1227], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.125912, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 29.836, "width_percent": 0.343}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 2 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.127964, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 30.179, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.131306, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 30.451, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.134774, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 30.777, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.13865, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 31.102, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1227 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1227], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.14151, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 31.419, "width_percent": 0.281}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.146784, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 31.7, "width_percent": 0.387}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.148997, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 32.087, "width_percent": 1.064}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.154051, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 33.152, "width_percent": 0.273}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.169395, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 33.424, "width_percent": 0.352}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1226", "type": "query", "params": [], "bindings": [1226], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.172839, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 33.776, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.1748579, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 34.093, "width_percent": 0.22}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.1777291, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 34.313, "width_percent": 0.396}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.182187, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 34.708, "width_percent": 0.29}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1226 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1226], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.184768, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 34.999, "width_percent": 0.264}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 98 limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1888669, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 35.263, "width_percent": 0.334}, {"sql": "select * from `media_files` where `media_files`.`id` = 4600 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4600], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.190883, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 35.597, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 98 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.194542, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 35.931, "width_percent": 0.299}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.208525, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 36.23, "width_percent": 0.361}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1225", "type": "query", "params": [], "bindings": [1225], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.211854, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 36.591, "width_percent": 2.208}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.216186, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 38.798, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.218898, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 39.071, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.221499, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 39.317, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1225 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1225], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.224735, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 39.546, "width_percent": 0.255}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 98 limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2289739, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 39.801, "width_percent": 0.361}, {"sql": "select * from `media_files` where `media_files`.`id` = 4600 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4600], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.231074, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 40.162, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 98 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.234352, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 40.417, "width_percent": 0.237}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.247373, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 40.654, "width_percent": 0.308}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1224", "type": "query", "params": [], "bindings": [1224], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.2502382, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 40.962, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.251957, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 41.217, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.254534, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 41.49, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1224 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1224], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.257696, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 41.763, "width_percent": 0.325}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 132 limit 1", "type": "query", "params": [], "bindings": [132], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.262158, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 42.088, "width_percent": 0.519}, {"sql": "select * from `media_files` where `media_files`.`id` = 7004 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7004], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2648501, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 42.607, "width_percent": 0.387}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 132 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 132], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.268606, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 42.994, "width_percent": 0.325}, {"sql": "select * from `countries` where `countries`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2889361, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 43.32, "width_percent": 0.44}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1223", "type": "query", "params": [], "bindings": [1223], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.292539, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 43.759, "width_percent": 0.361}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 2 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.294825, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 44.12, "width_percent": 0.29}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 7 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.298368, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 44.41, "width_percent": 0.413}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.301502, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 44.824, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.304489, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 45.088, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.308841, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 45.404, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1223 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1223], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3115761, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 45.73, "width_percent": 0.431}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.316135, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 46.161, "width_percent": 0.378}, {"sql": "select * from `media_files` where `media_files`.`id` = 3233 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3233], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.318132, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 46.539, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 74 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 74], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 197}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.321532, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 46.855, "width_percent": 0.273}, {"sql": "select * from `countries` where `countries`.`id` = 52 limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.335783, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 47.128, "width_percent": 0.422}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1222", "type": "query", "params": [], "bindings": [1222], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.339759, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 47.55, "width_percent": 0.413}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3421159, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.964, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.344517, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.298, "width_percent": 0.299}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.347444, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.597, "width_percent": 0.343}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.349858, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.94, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.351974, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.195, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.355142, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.45, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.357258, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.714, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.359034, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.969, "width_percent": 0.405}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3628302, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.374, "width_percent": 0.431}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.364534, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.805, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.367186, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.13, "width_percent": 0.317}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.380229, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.447, "width_percent": 0.431}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3827488, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.878, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38416, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.212, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386513, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.529, "width_percent": 0.308}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388622, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.837, "width_percent": 0.308}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390798, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.145, "width_percent": 0.299}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392993, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.444, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396588, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.76, "width_percent": 0.396}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399082, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.156, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401526, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.473, "width_percent": 0.378}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.404732, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.851, "width_percent": 0.352}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406059, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.203, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408514, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.467, "width_percent": 0.246}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421427, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.713, "width_percent": 0.334}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4237618, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.047, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425203, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.381, "width_percent": 0.237}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42728, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.619, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429516, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.848, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431923, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.103, "width_percent": 0.22}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4339218, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.323, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.436775, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.534, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438695, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.745, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4403381, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.956, "width_percent": 0.273}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443315, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.229, "width_percent": 0.264}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.444521, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.492, "width_percent": 0.352}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.447365, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.844, "width_percent": 0.255}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459206, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.099, "width_percent": 0.264}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4614599, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.363, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.462863, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.689, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.465873, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.961, "width_percent": 0.361}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4682078, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.322, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4701698, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.551, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.472121, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.779, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4740841, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.008, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.476712, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.254, "width_percent": 0.246}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.481274, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.501, "width_percent": 0.378}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.482669, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.879, "width_percent": 0.237}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4853332, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.116, "width_percent": 0.246}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.49782, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.363, "width_percent": 0.352}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.500354, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.714, "width_percent": 0.237}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5015419, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.952, "width_percent": 0.22}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5035071, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.172, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5054948, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.436, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5074198, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.647, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.509946, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.858, "width_percent": 0.255}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5135021, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.113, "width_percent": 0.387}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5166402, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.5, "width_percent": 0.255}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.529625, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.755, "width_percent": 0.369}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.532392, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.124, "width_percent": 0.237}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.533573, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.362, "width_percent": 0.22}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.535553, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.582, "width_percent": 0.22}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.538396, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.802, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.540004, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.013, "width_percent": 0.237}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.543612, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.25, "width_percent": 0.29}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.545124, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.541, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.549119, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.875, "width_percent": 0.334}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.562913, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.209, "width_percent": 0.361}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565933, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.57, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5674958, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.895, "width_percent": 0.22}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.569532, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.115, "width_percent": 0.202}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.572517, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.317, "width_percent": 0.229}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.576053, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.546, "width_percent": 0.317}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5775218, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.863, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5809002, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.091, "width_percent": 0.396}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5955992, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.487, "width_percent": 0.422}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.598546, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.909, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5999029, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.235, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6022859, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.464, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.604361, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.692, "width_percent": 0.194}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.607553, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.886, "width_percent": 0.299}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.610601, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.185, "width_percent": 0.325}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.614772, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.51, "width_percent": 0.325}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.616076, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.836, "width_percent": 0.202}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618697, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.038, "width_percent": 0.194}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631111, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.232, "width_percent": 0.325}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.633425, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.557, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6437821, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.803, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646629, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.076, "width_percent": 0.299}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.64878, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.375, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6509001, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.621, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6528592, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.868, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6551511, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.114, "width_percent": 0.581}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.658616, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.694, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660587, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.949, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66351, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.275, "width_percent": 0.325}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.667319, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.6, "width_percent": 0.334}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668564, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.935, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671303, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.207, "width_percent": 0.237}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683826, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.445, "width_percent": 0.361}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6862168, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.805, "width_percent": 0.343}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687596, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.148, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689599, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.395, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.692248, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.641, "width_percent": 0.255}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695598, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.896, "width_percent": 0.369}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.697025, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.265, "width_percent": 0.387}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700053, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.652, "width_percent": 0.361}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714914, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.013, "width_percent": 0.387}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.717328, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.4, "width_percent": 0.281}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71923, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.682, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721514, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.007, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.723478, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.253, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7254481, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.5, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.728468, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.746, "width_percent": 0.299}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7308462, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.045, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.733597, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.309, "width_percent": 0.264}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736958, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.573, "width_percent": 0.422}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738403, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.995, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7410262, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.268, "width_percent": 0.273}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7536259, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.54, "width_percent": 0.343}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755943, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.883, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7571251, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.138, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7591212, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.393, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762206, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.64, "width_percent": 0.308}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7642322, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.948, "width_percent": 0.255}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.767445, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.203, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7703362, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.475, "width_percent": 0.255}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.783264, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.73, "width_percent": 0.361}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7856522, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.091, "width_percent": 0.281}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7869432, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.373, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7889562, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.628, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.790997, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.874, "width_percent": 0.237}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7929611, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.111, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.795099, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.358, "width_percent": 0.325}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.797703, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.683, "width_percent": 0.361}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801239, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.044, "width_percent": 0.308}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8031101, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.352, "width_percent": 0.264}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8061469, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.615, "width_percent": 0.325}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.807466, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.941, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.810236, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.205, "width_percent": 0.299}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8236709, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.504, "width_percent": 0.352}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.825962, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.856, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8272872, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.128, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8295739, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.383, "width_percent": 0.352}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.831968, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.735, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.834093, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.999, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.836194, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.245, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.838161, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.492, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841064, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.738, "width_percent": 0.211}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.843591, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.949, "width_percent": 0.273}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8478482, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.222, "width_percent": 0.396}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.849299, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.618, "width_percent": 0.281}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.852024, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.899, "width_percent": 0.255}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8648589, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.154, "width_percent": 0.352}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.86735, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.506, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8686311, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.779, "width_percent": 0.281}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.870677, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.06, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.872641, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.306, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889602, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.553, "width_percent": 0.369}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.892035, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.922, "width_percent": 0.237}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.895331, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.16, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.897593, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.494, "width_percent": 0.29}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.900928, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.784, "width_percent": 0.281}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9021032, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.066, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.904742, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.321, "width_percent": 0.237}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9175508, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.558, "width_percent": 0.361}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.919907, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.919, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.921175, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.183, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.923141, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.411, "width_percent": 0.229}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9252791, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.64, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927951, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.886, "width_percent": 0.308}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931292, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.194, "width_percent": 0.334}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93261, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.528, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935276, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.801, "width_percent": 0.237}, {"sql": "select * from `ads`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.997393, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.039, "width_percent": 0.317}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1882288, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.355, "width_percent": 0.369}, {"sql": "select * from `slugs_translations` where `slugs_translations`.`slugs_id` in (131)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1889842, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.725, "width_percent": 0.246}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.190586, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.971, "width_percent": 0.281}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193681, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.252, "width_percent": 0.229}, {"sql": "select * from `slugs_translations` where `slugs_translations`.`slugs_id` in (131)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194207, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.481, "width_percent": 0.211}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.195806, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.692, "width_percent": 0.308}]}, "models": {"data": {"Xmetr\\Base\\Models\\MetaBox": {"value": 255, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 132, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 75, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Currency": {"value": 46, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\Location\\Models\\Country": {"value": 42, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php:1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 27, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 24, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 24, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Media\\Models\\MediaFile": {"value": 22, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFile.php:1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 9, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\SpokenLanguage": {"value": 9, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FSpokenLanguage.php:1", "ajax": false, "filename": "SpokenLanguage.php", "line": "?"}}, "Xmetr\\LanguageAdvanced\\Models\\TranslationResolver": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FModels%2FTranslationResolver.php:1", "ajax": false, "filename": "TranslationResolver.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 4, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}, "Xmetr\\Page\\Models\\Page": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php:1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 680, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/rent-properties", "action_name": "public.single", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getView", "uri": "GET en/{slug?}", "controller": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getView<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:41\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/en", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:41\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/PublicController.php:41-93</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "2.64s", "peak_memory": "70MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-744501958 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-744501958\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-882847412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-882847412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1923485669 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6ImJjZmY3MjVlLTg2M2ItNDViZC05OWUwLTI1Zjg0Y2YyNTEwNSIsImMiOjE3NTUwMjcwOTM1OTAsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; XSRF-TOKEN=eyJpdiI6ImFNZko5RUpvaStUK0dRdXBuakNBanc9PSIsInZhbHVlIjoiSUNhZ3RWQkZNUEhOZE4yRkxKbXF4dzM1SWtaR3padHFtYUNMZXoyMmlIOFhENWsyNXkvZ2t4UDAvMEZBR3hWR2lWdzBWL0hOTWNVMXpjYnlrTVI0QWhURzBzWkNNaGpuakVSWnFhYWxTSTZ5dHBRdHpib1d5UlJGQzUxNFpTTzAiLCJtYWMiOiJiYzJjZmNmMzQyNjY0ZjM5NDNlYmNlODViOWU0OTAzMmViNGEyOTAyMzE5YjhiNGM2MDk0ODE0OWJiYzJkMDU0IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IjdaQjIrNDVHZXFmR2g5aC9TckVPd0E9PSIsInZhbHVlIjoiUnNQTVcvcSs5R3VOaUhjRkVBK1FhRXhMaDkwOWg5NW5zSnQ0NUg0WnVwSUNIMnBNaFVqYitiRGR1YXpPcVhBRzNOKyt3NlhMQ3ZFekJJY0s2K2x5ejdhOUxQQTVxbE5LcGdITEFkTWhyb0VuRVRGOCttbkU2cDY2ejVRYW85SGMiLCJtYWMiOiJhZTQ1MzdjODEzMmYwMTFkMGRkZDU1NTE1YWI0MWFkMmIwMWRiYmU4MzEwMDY3NzA2NDllODZmMjRlMWUwYjc2IiwidGFnIjoiIn0%3D; _ga_KQ6X76DET4=GS2.1.s1755027093$o110$g1$t1755032108$j60$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923485669\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1058367709 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h7bKZg3z6MqqFVW5H8LdcPXPIAeeQnbnWZpFJ4ZR</span>\"\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058367709\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-734260959 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 21:00:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>xmetr-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.4.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>developer-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>project-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Xmetr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734260959\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1733492810 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">https://xmetr.gc/en/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733492810\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/rent-properties", "action_name": "public.single", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getView"}, "badge": null}}