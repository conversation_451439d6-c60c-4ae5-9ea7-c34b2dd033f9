<?php $__env->startSection('content'); ?>
    <style>
        .badge {
            border-radius: 5px;
            font-size: 13px;
            padding-top: 5px;
            padding-bottom: 5px;
            padding-left: 10px;
            padding-right: 10px;
            line-height: 1.85;
        }

        .status-pill {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: inline-block;
            margin: 0 4px;
        }

        .status-pill.active {
            background-color: #5E2DC2;
            color: white;
            border-color: #5E2DC2;
        }

        .status-pill.inactive {
            background-color: #F7F7F7;
            color: #717171;
            border-color: #D6D6D7;
        }

        .status-pill:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .status-pills-container {
            display: flex;
            justify-content: center;
            margin: 10px 0;
            gap: 8px;
            flex-wrap: wrap;
        }

        .status-pills-container.justify-start {
            justify-content: flex-start;
        }

        .status-pill.active:hover {
            background-color: #4a1d96;
            border-color: #4a1d96;
        }

        .status-pill.inactive:hover {
            background-color: #e5e5e5;
            border-color: #b0b0b0;
        }
    </style>
    <div class="body_content bg-[#F7F7F7] pt-[24px]">
        <div class="w-full h-full flex flex-col items-center pb-[40px] gap-[24px]">
            <h4 class="title text-center"><?php echo e(__('My listings')); ?></h4>

           
            <?php if($not_rented_properties->isNotEmpty()): ?>
<div id="active-section"></div>
             <!-- Status Navigation Pills -->
            <div class="status-pills-container">
                <a href="#active-section" class="status-pill active">
                    <?php echo e(__('Active')); ?>

                </a>
                <a href="#rented-section" class="status-pill">
                    <?php echo e(__('Rented')); ?>

                </a>
            </div>

                <div  class="max-w-[480px] w-full rounded-[15px] bg-white p-[20px] h-fit flex flex-col gap-[20px]"
                    style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
                    <h4 class="text-black text-[20px] font-bold"><?php echo e(__('Active')); ?></h4>

                    <div class="flex flex-col gap-[20px]">
                        <?php $__currentLoopData = $not_rented_properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div
                                class="flex flex-col grow m-0 max-w-[550px] bg-white rounded-t-[10px] rounded-b-[10px] overflow-hidden">
                                <a href="<?php echo e($property->url); ?>"
                                    class="relative flex flex-col justify-between p-[20px] w-full aspect-[16/10] list-thumb shrink-0">

                                    
                                    <?php echo e(RvMedia::image($property->image, $property->name, 'medium-rectangle', false, ['class' => 'absolute top-0 left-0 w-full h-full object-cover'])); ?>

                                   
                                    <div class="flex justify-between gap-[16px] z-[2]">
                                        <!-- Badges -->
                                        <div class="flex gap-[5px] flex-wrap items-start">
                                            <?php echo $property->moderation_status->toHtml(); ?>

                                        </div>

                                        <form action="<?php echo e(route('public.account.properties.destroy', $property->id)); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" onclick="return confirm('Are you sure you want to delete this item?');"
                                                class="p-[15px] bg-[#FBF0EE] rounded-[10px] w-[50px] h-[50px] relative">
                                                <svg width="18" height="21" viewBox="0 0 18 21" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M0 5.05392C0 4.56947 0.345378 4.17674 0.771426 4.17674L3.43565 4.17627C3.965 4.16101 4.432 3.77828 4.61212 3.21208C4.61686 3.19719 4.6223 3.17883 4.64183 3.1122L4.75663 2.72052C4.82688 2.48037 4.88808 2.27114 4.97373 2.08413C5.31206 1.34533 5.93805 0.83229 6.66144 0.70094C6.84454 0.667691 7.03846 0.667831 7.26106 0.667991H10.739C10.9616 0.667831 11.1555 0.667691 11.3386 0.70094C12.062 0.83229 12.688 1.34533 13.0263 2.08413C13.112 2.27114 13.1732 2.48037 13.2434 2.72052L13.3582 3.1122C13.3777 3.17883 13.3832 3.19719 13.3879 3.21208C13.5681 3.77828 14.1277 4.16148 14.657 4.17674H17.2285C17.6545 4.17674 17.9999 4.56947 17.9999 5.05392C17.9999 5.53838 17.6545 5.9311 17.2285 5.9311H0.771426C0.345378 5.9311 0 5.53838 0 5.05392Z"
                                                        fill="#DC6F5A" />
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M8.59556 20.668H9.40435C12.187 20.668 13.5784 20.668 14.483 19.7821C15.3877 18.8962 15.4802 17.443 15.6653 14.5365L15.932 10.3486C16.0325 8.77163 16.0827 7.98317 15.6288 7.48352C15.175 6.98386 14.4086 6.98386 12.8759 6.98386H5.12401C3.59125 6.98386 2.82487 6.98386 2.37104 7.48352C1.91721 7.98317 1.96743 8.77163 2.06787 10.3486L2.33458 14.5365C2.51969 17.443 2.61224 18.8962 3.51687 19.7821C4.42151 20.668 5.81286 20.668 8.59556 20.668ZM7.24626 10.8565C7.20506 10.4227 6.8375 10.1061 6.42534 10.1495C6.01318 10.1929 5.71248 10.5798 5.75369 11.0136L6.25369 16.2768C6.29491 16.7106 6.66244 17.0272 7.07456 16.9838C7.48676 16.9404 7.78746 16.5535 7.74626 16.1197L7.24626 10.8565ZM11.5745 10.1495C11.9867 10.1929 12.2874 10.5798 12.2462 11.0136L11.7462 16.2768C11.705 16.7106 11.3374 17.0272 10.9253 16.9838C10.5131 16.9404 10.2124 16.5535 10.2536 16.1197L10.7536 10.8565C10.7948 10.4227 11.1624 10.1061 11.5745 10.1495Z"
                                                        fill="#DC6F5A" />
                                                </svg>
                                            </button>

                                        </form>

                                    </div>

                                    <div class="px-[10px] py-[5px] rounded-[5px] z-[2] bg-white w-fit"
                                        style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
                                        <p class="text-black text-[17px]">
                                            <b class="font-bold">
                                                <?php if($property->currency): ?> <?php echo e($property->currency->symbol); ?> <?php else: ?> $ <?php endif; ?> <?php echo e(number_format($property->price)); ?>

                                            </b> / <?php echo e(strtolower($property->period->label())); ?></p>
                                    </div>

                                </a>

                                <div
                                    class="px-[20px] pt-[20px] pb-[30px] bg-white border border-[#D6D6D7] rounded-b-[10px] flex flex-col gap-[15px]">

                                    <div class="flex items-center gap-[5px] flex-wrap">


                                        <?php if($property->city->name): ?>
                                        <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                                            <p class="text-[13px] text-[#717171]"><?php echo e($property->country->name); ?> , <?php echo e($property->city->name); ?></p>
                                        </div>
                                        <?php endif; ?>

                                    </div>

                                    <div class="flex items-center gap-[20px] flex-wrap">
                                        <?php if($property->number_bedroom): ?>
                                            <p class="text-[15px] text-black">🛌
                                                <?php echo e(number_format($property->number_bedroom)); ?> <?php echo e(__('bedroom')); ?></p>
                                        <?php endif; ?>
                                        <?php if($property->number_bathroom): ?>
                                            <p class="text-[15px] text-black">🛀🏻
                                                <?php echo e(number_format($property->number_bathroom)); ?> <?php echo e(__('bathroom')); ?></p>
                                        <?php endif; ?>
                                        <?php if($property->square): ?>
                                            <p class="text-[15px] text-black">📐 <?php echo e($property->square_text); ?></p>
                                        <?php endif; ?>
                                    </div>

                                    

                                    <span class="w-full h-[1px] bg-[#D6D6D7] block"></span>

                                    <!-- Details -->
                                    <div class="w-full flex items-center justify-between gap-[4px]">
                                        <!-- Premium -->
                                        <!-- <div class="px-[10px] py-[3px] bg-[#5E2DC2] rounded-[5px]">
                          <p class="text-[13px] text-white font-bold">🔥 Premium</p>
                        </div> -->

                                        <!-- Make premium -->
                                        
                                            <!-- <a href="#modalSelectPremium" role="button" data-bs-toggle="modal" class="px-[10px] py-[3px] bg-[#5E2DC2] rounded-[5px]" data-bs-toggle="tooltip" title="Make premium">
                            <p class="text-[13px] text-white font-bold">🔥</p>
                          </a> -->

                                            
                                        

                                        <div class="flex items-center gap-[4px]">
                                            <p class="text-[15px]">🕒</p>
                                            <p class="text-[13px] text-black">
                                                <?php echo e(Theme::formatDate($property->created_at)); ?></p>
                                        </div>

                                        <div class="flex items-center gap-[4px]">
                                            <p class="text-[15px]">👁️</p>
                                            <p class="text-[13px] text-black">
                                                <?php if($property->views === 1): ?>
                                                    <?php echo e(__('1 Views')); ?>

                                                <?php else: ?>
                                                    <?php echo e(__(':number Views', ['number' => number_format($property->views)])); ?>

                                                <?php endif; ?>
                                            </p>
                                        </div>

                                    </div>

                                    <span class="w-full h-[1px] bg-[#D6D6D7] block"></span>

                                    <!-- Actions -->
                                    <div class="flex gap-[10px] items-stretch">
                                        <a href="<?php echo e(route('public.account.properties.edit', $property->id)); ?>"
                                            class="rounded-[10px] bg-[#5E2DC2] duration-200 hover:bg-[#5026a5] px-[8px] py-[15px] w-full text-center">
                                            <p class="text-[15px] text-white font-bold"><?php echo e(__('Edit')); ?></p>
                                        </a>
                                        <?php if($property->moderation_status == \Xmetr\RealEstate\Enums\ModerationStatusEnum::APPROVED): ?>
                                            <form action="<?php echo e(route('public.account.properties.rent', $property->id)); ?>"
                                                method="POST" class="rounded-[10px]  w-full h-full">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit"
                                                    class="rounded-[10px] bg-[#212329] duration-200 hover:bg-[#3e424d] px-[8px] py-[15px] w-full h-full">
                                                    <p class="text-[15px] text-white font-bold">🤘 <?php echo e(__('Rented')); ?></p>
                                                </button>

                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                </div>
            <?php endif; ?>

            <?php if($rented_properties->isNotEmpty()): ?>
<div id="rented-section"></div>
             <div class="status-pills-container">
                <a href="#active-section" class="status-pill">
                    <?php echo e(__('Active')); ?>

                </a>
                <a href="#rented-section" class="status-pill active">
                    <?php echo e(__('Rented')); ?>

                </a>
            </div>

                <div  class="max-w-[480px] w-full rounded-[15px] bg-white p-[20px] h-fit flex flex-col gap-[20px]"
                    style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
                    <h4 class="text-black text-[20px] font-bold"><?php echo e(__('Rented')); ?></h4>

                    <div class="flex flex-col gap-[20px]">
                        <?php $__currentLoopData = $rented_properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex flex-col grow m-0 max-w-[550px]  bg-white rounded-t-[10px] rounded-b-[10px] overflow-hidden">
                                <a href="<?php echo e($property->url); ?>"
                                    class="relative flex flex-col justify-between p-[20px] w-full aspect-[16/10] list-thumb shrink-0">

                                    <?php echo e(RvMedia::image($property->image, $property->name, 'medium-rectangle', false, ['class' => 'absolute top-0 left-0 w-full h-full object-cover'])); ?>


                                    <div class="flex justify-between gap-[16px] z-[2]">
                                        <!-- Badges -->
                                        <div class="flex items-center gap-[5px]"></div>

                                        <form action="<?php echo e(route('public.account.properties.destroy', $property->id)); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" onclick="return confirm('Are you sure you want to delete this item?');"
                                                class="p-[15px] bg-[#FBF0EE] rounded-[10px] w-[50px] h-[50px] relative">
                                                <svg width="18" height="21" viewBox="0 0 18 21" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M0 5.05392C0 4.56947 0.345378 4.17674 0.771426 4.17674L3.43565 4.17627C3.965 4.16101 4.432 3.77828 4.61212 3.21208C4.61686 3.19719 4.6223 3.17883 4.64183 3.1122L4.75663 2.72052C4.82688 2.48037 4.88808 2.27114 4.97373 2.08413C5.31206 1.34533 5.93805 0.83229 6.66144 0.70094C6.84454 0.667691 7.03846 0.667831 7.26106 0.667991H10.739C10.9616 0.667831 11.1555 0.667691 11.3386 0.70094C12.062 0.83229 12.688 1.34533 13.0263 2.08413C13.112 2.27114 13.1732 2.48037 13.2434 2.72052L13.3582 3.1122C13.3777 3.17883 13.3832 3.19719 13.3879 3.21208C13.5681 3.77828 14.1277 4.16148 14.657 4.17674H17.2285C17.6545 4.17674 17.9999 4.56947 17.9999 5.05392C17.9999 5.53838 17.6545 5.9311 17.2285 5.9311H0.771426C0.345378 5.9311 0 5.53838 0 5.05392Z"
                                                        fill="#DC6F5A" />
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M8.59556 20.668H9.40435C12.187 20.668 13.5784 20.668 14.483 19.7821C15.3877 18.8962 15.4802 17.443 15.6653 14.5365L15.932 10.3486C16.0325 8.77163 16.0827 7.98317 15.6288 7.48352C15.175 6.98386 14.4086 6.98386 12.8759 6.98386H5.12401C3.59125 6.98386 2.82487 6.98386 2.37104 7.48352C1.91721 7.98317 1.96743 8.77163 2.06787 10.3486L2.33458 14.5365C2.51969 17.443 2.61224 18.8962 3.51687 19.7821C4.42151 20.668 5.81286 20.668 8.59556 20.668ZM7.24626 10.8565C7.20506 10.4227 6.8375 10.1061 6.42534 10.1495C6.01318 10.1929 5.71248 10.5798 5.75369 11.0136L6.25369 16.2768C6.29491 16.7106 6.66244 17.0272 7.07456 16.9838C7.48676 16.9404 7.78746 16.5535 7.74626 16.1197L7.24626 10.8565ZM11.5745 10.1495C11.9867 10.1929 12.2874 10.5798 12.2462 11.0136L11.7462 16.2768C11.705 16.7106 11.3374 17.0272 10.9253 16.9838C10.5131 16.9404 10.2124 16.5535 10.2536 16.1197L10.7536 10.8565C10.7948 10.4227 11.1624 10.1061 11.5745 10.1495Z"
                                                        fill="#DC6F5A" />
                                                </svg>
                                            </button>

                                        </form>
                                    </div>

                                    <div
                                        class="absolute top-0 left-0 w-full h-full z-[1] bg-[#212329]/[.8] flex justify-center items-center">
                                        <div class="p-[10px] bg-white rounded-[5px]">
                                            <p class="text-black text-[20px] font-bold">🙌 <?php echo e(__('Rented')); ?></p>
                                        </div>
                                    </div>

                                </a>

                                <div
                                    class="px-[20px] pt-[20px] pb-[30px] bg-white border border-[#D6D6D7] rounded-b-[10px] flex flex-col gap-[15px]">


                                        <div class="flex items-center gap-[5px] flex-wrap">

                                            <?php if($property->country->name): ?>
                                            <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                                                <p class="text-[13px] text-[#717171]"><?php echo e($property->country->name); ?></p>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($property->city->name): ?>
                                            <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                                                <p class="text-[13px] text-[#717171]"><?php echo e($property->city->name); ?></p>
                                            </div>
                                            <?php endif; ?>

                                        </div>


                                    <div class="flex items-center gap-[20px] flex-wrap">
                                        <?php if($property->number_bedroom): ?>
                                            <p class="text-[15px] text-black">🛌
                                                <?php echo e(number_format($property->number_bedroom)); ?> <?php echo e(__('bedroom')); ?></p>
                                        <?php endif; ?>
                                        <?php if($property->number_bathroom): ?>
                                            <p class="text-[15px] text-black">🛀🏻
                                                <?php echo e(number_format($property->number_bathroom)); ?> <?php echo e(__('bathroom')); ?></p>
                                        <?php endif; ?>
                                        <?php if($property->square): ?>
                                            <p class="text-[15px] text-black">📐 <?php echo e($property->square_text); ?></p>
                                        <?php endif; ?>
                                    </div>

                                    

                                    <span class="w-full h-[1px] bg-[#D6D6D7] block"></span>

                                    <!-- Details -->
                                    <div class="w-full flex items-center justify-between gap-[4px]">
                                        <!-- Premium -->
                                        <!-- <div class="px-[10px] py-[3px] bg-[#5E2DC2] rounded-[5px]">
                          <p class="text-[13px] text-white font-bold">🔥 Premium</p>
                        </div> -->

                                        <!-- Make premium -->
                                        
                                            <!-- <a href="#modalSelectPremium" role="button" data-bs-toggle="modal" class="px-[10px] py-[3px] bg-[#5E2DC2] rounded-[5px]" data-bs-toggle="tooltip" title="Make premium">
                            <p class="text-[13px] text-white font-bold">🔥</p>
                          </a> -->

                                            
                                        

                                        <div class="flex items-center gap-[4px]">
                                            <p class="text-[15px]">🕒</p>
                                            <p class="text-[13px] text-black">
                                                <?php echo e(Theme::formatDate($property->created_at)); ?></p>
                                        </div>

                                        <div class="flex items-center gap-[4px]">
                                            <p class="text-[15px]">👁️</p>
                                            <p class="text-[13px] text-black">
                                                <?php if($property->views === 1): ?>
                                                    <?php echo e(__('1 Views')); ?>

                                                <?php else: ?>
                                                    <?php echo e(__(':number Views', ['number' => number_format($property->views)])); ?>

                                                <?php endif; ?>
                                            </p>
                                        </div>

                                    </div>


                                    <span class="w-full h-[1px] bg-[#D6D6D7] block"></span>

                                    <!-- Actions -->
                                    <div class="flex gap-[10px] items-stretch">
                                        <a href="<?php echo e(route('public.account.properties.edit', $property->id)); ?>"
                                            class="rounded-[10px] bg-[#5E2DC2] duration-200 hover:bg-[#5026a5] px-[8px] py-[15px] w-full text-center">
                                            <p class="text-[15px] text-white font-bold"><?php echo e(__('Edit')); ?></p>
                                        </a>

                                        <form action="<?php echo e(route('public.account.properties.recover', $property->id)); ?>"
                                            method="POST" class="rounded-[10px]  w-full h-full">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit"
                                                class="rounded-[10px] bg-[#212329] duration-200 hover:bg-[#3e424d] px-[8px] py-[15px] w-full h-full">
                                                <p class="text-[15px] text-white font-bold"><?php echo e(__('Recover')); ?></p>
                                            </button>

                                        </form>

                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                </div>
            <?php endif; ?>
        </div>
    </div>


     <div class="fixed left-0 bottom-0 w-full md:hidden flex gap-[10px] items-stretch py-[20px] px-[30px] z-[3]">
        <a href="https://t.me/xmetrsupport" class="x-filter-map_trigger shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[10px] px-[20px] h-auto z-[4] grow flex-1">
            <p class="text-white text-[15px]"><?php echo e(__('Support')); ?></p>
        </a>

        <a href="<?php echo e(route('public.account.properties.create')); ?>"  class="x-fixedParent_trigger bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center justify-center gap-[10px] relative shrink-0 z-[4] grow flex-1">
            <p class="text-white text-[15px] font-bold"><?php echo e(__('Add Listing')); ?></p>
        </a>

        <div class="absolute bottom-0 left-0 w-full h-[95px]" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            const pillLinks = document.querySelectorAll('.status-pill[href^="#"]');
            pillLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('plugins/real-estate::themes.dashboard.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\xmetr\platform/plugins/real-estate/resources/views/account/properties/list.blade.php ENDPATH**/ ?>