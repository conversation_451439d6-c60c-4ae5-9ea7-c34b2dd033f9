<?php
    $icon_image = $shortcode->icon_image ? RvMedia::getImageUrl($shortcode->icon_image) : null;
?>
<section class="w-full py-[240px] flex justify-center items-center bg-[#713AF6] max-[768px]:pt-[180px] max-[768px]:pb-[130px]">
    <div class="container flex flex-col gap-[40px] items-center w-full">
      <!-- Heading -->
      <div class="flex flex-col gap-[10px]">
        <div class="flex items-center gap-[10px] pb-[20px]">
          <h2 class="text-white !text-[70px] font-bold max-[768px]:!text-[40px] max-[480px]:text-[24px]"><?php echo BaseHelper::clean($shortcode->title_left); ?></h2>
          <div class="relative flex flex-col gap-[10px] shrink-0">
            <img src="<?php echo e($icon_image); ?>" class="max-[480px]:w-[100px] max-[480px]:h-[100px]" alt="..." width="150px" height="150px">
            <p class="absolute -bottom-[30px] left-[50%] -translate-x-[50%] whitespace-nowrap text-white text-[20px] text-center"><?php echo BaseHelper::clean($shortcode->description); ?></p>
          </div>

          <div class="relative">
            <h2 class="text-white !text-[70px] font-bold max-[768px]:!text-[40px] max-[480px]:text-[24px]"><?php echo BaseHelper::clean($shortcode->title_right); ?></h2>

            <div class="bg-[#F8CC51] w-[130px] rounded-[5px] px-[16px] py-[2px] flex justify-center items-center gap-[4px] absolute bottom-[5px] left-[50%] -translate-x-[50%] max-[768px]:-bottom-[5px]">
              <p class="text-black text-[14px] font-bold max-[768px]:text-[13px]" id="x-home-countryLabel"><?php echo e(__('All Countries')); ?></p>
            </div>
          </div>
        </div>
      </div>

      <div class="flex flex-col items-center gap-[30px] w-full">

            <form id="hero-search-form" action="<?php echo e(RealEstateHelper::getPropertiesListPageUrl()); ?>" method="GET" class="max-w-[1000px] bg-white flex items-stretch py-[20px] rounded-[15px] w-full max-[768px]:flex-col max-[768px]:gap-[20px]">
                <!-- Accordion -->
                <div class="x-home-accordion w-full relative px-[30px]">
                  <!-- Header -->
                  <div class="flex items-center h-full gap-[8px] justify-between w-full">
                    <select id="home-country-select" class="form-select w-full h-full appearance-none border-none text-black text-[20px] font-bold" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMjIiIHZpZXdCb3g9IjAgMCAxOCAyMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOS41OTA2NSAxOC42ODk5TDkuNTkwNjIgMTguNjg5OUM5LjQ3MDQ5IDE4Ljc3ODEgOS4zNTcyOCAxOC44NTkzIDkuMjUyMDYgMTguOTMzNkM5LjEzODkxIDE4Ljg1NzYgOS4wMTU2IDE4Ljc3MjMgOC44ODMzOCAxOC42Nzc4QzguMTUzOTggMTguMTU1OSA3LjIwNzQxIDE3LjM4OTMgNi4yNzUzNSAxNi40MzM0QzQuMzYwNDcgMTQuNDY5NCAyLjc2NjY3IDExLjk3NzggMi43NjY2NyA5LjMzMzMzQzIuNzY2NjcgNS43MDY2NiA1LjcwNjY2IDIuNzY2NjcgOS4zMzMzMyAyLjc2NjY3QzEyLjk2IDIuNzY2NjcgMTUuOSA1LjcwNjY2IDE1LjkgOS4zMzMzM0MxNS45IDExLjk0NzMgMTQuMjM3IDE0LjQzMTMgMTIuMjUwNyAxNi40MTc0TDEyLjI1MDcgMTYuNDE3NEMxMS4yODg5IDE3LjM3OTMgMTAuMzIwNiAxOC4xNTQ3IDkuNTkwNjUgMTguNjg5OVpNOS4zMzMzMyAyMS4xSDkuMzYxMDNMOS4zODQ3OCAyMS4wODU3TDkuMzMzMzMgMjFDOS4zODQ3OCAyMS4wODU3IDkuMzg0ODQgMjEuMDg1NyA5LjM4NDkzIDIxLjA4NTdMOS4zODUyNiAyMS4wODU1TDkuMzg2NSAyMS4wODQ3TDkuMzkxMjIgMjEuMDgxOUw5LjQwOTQ5IDIxLjA3MDdDOS40MjU1NCAyMS4wNjA5IDkuNDQ5MjIgMjEuMDQ2MyA5LjQ4MDAyIDIxLjAyN0M5LjU0MTYzIDIwLjk4ODMgOS42MzE3NiAyMC45MzEgOS43NDYzMiAyMC44NTU3QzkuOTc1NDQgMjAuNzA1MSAxMC4zMDI0IDIwLjQ4MjggMTAuNjk0NiAyMC4xOTUyQzExLjQ3ODcgMTkuNjIwMiAxMi41MjQ1IDE4Ljc4MzYgMTMuNTcwNyAxNy43Mzc0QzE1LjY1OTUgMTUuNjQ4NiAxNy43NjY3IDEyLjcwNzQgMTcuNzY2NyA5LjMzMzMzQzE3Ljc2NjcgNC42NzU3MyAxMy45OTA5IDAuOSA5LjMzMzMzIDAuOUM0LjY3NTczIDAuOSAwLjkgNC42NzU3MyAwLjkgOS4zMzMzM0MwLjkgMTIuNzA1NyAyLjkwMDk4IDE1LjY0NjQgNC45Mzg4MiAxNy43MzY1QzUuOTU5MTcgMTguNzgzIDYuOTkyNDYgMTkuNjIwMSA3Ljc5NzI4IDIwLjE5NTlDOC4xOTk2NyAyMC40ODM4IDguNTQ1NyAyMC43MDY5IDguODA1MTkgMjAuODU4NUM4LjkzNDc5IDIwLjkzNDIgOS4wNDM5MSAyMC45OTI3IDkuMTI4MzMgMjEuMDMyNkM5LjE3MDQzIDIxLjA1MjUgOS4yMDc3OCAyMS4wNjg1IDkuMjM5MjkgMjEuMDc5N0M5LjI2NzQgMjEuMDg5NyA5LjMwMTc4IDIxLjEgOS4zMzMzMyAyMS4xWk0xMC45IDkuMzMzMzNDMTAuOSAxMC4xOTg2IDEwLjE5ODUgMTAuOSA5LjMzMzMzIDEwLjlDOC40NjgxMiAxMC45IDcuNzY2NjcgMTAuMTk4NiA3Ljc2NjY3IDkuMzMzMzNDNy43NjY2NyA4LjQ2ODA5IDguNDY4MTIgNy43NjY2NyA5LjMzMzMzIDcuNzY2NjdDMTAuMTk4NiA3Ljc2NjY3IDEwLjkgOC40NjgwOSAxMC45IDkuMzMzMzNaTTkuMzMzMzMgMTIuNzY2N0MxMS4yMjk2IDEyLjc2NjcgMTIuNzY2NyAxMS4yMjk2IDEyLjc2NjcgOS4zMzMzM0MxMi43NjY3IDcuNDM3MTYgMTEuMjI5NiA1LjkgOS4zMzMzMyA1LjlDNy40MzcxIDUuOSA1LjkgNy40MzcxNiA1LjkgOS4zMzMzM0M1LjkgMTEuMjI5NiA3LjQzNzEgMTIuNzY2NyA5LjMzMzMzIDEyLjc2NjdaIiBmaWxsPSIjNzE3MTcxIiBzdHJva2U9IiM3MTcxNzEiIHN0cm9rZS13aWR0aD0iMC4yIi8+PC9zdmc+'), url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxNCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCA3LjU5NTVMMi4zMzMzMyAxMEw3IDUuMzMzMzNMMTEuNjY2NyAxMEwxNCA3LjU5NTVMNyAwLjY2NjY2NkwwIDcuNTk1NVoiIGZpbGw9ImJsYWNrIi8+PC9zdmc+'); background-position: 20px center, right 20px center; background-repeat: no-repeat, no-repeat; padding-left: 50px; background-size: 20px 30px, auto;">
                      <option value="" selected><?php echo e(__('Location')); ?></option>
                      <?php $__currentLoopData = get_all_countries(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                          <option value="<?php echo e($country->url); ?>"><?php echo e($country->name); ?></option>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                  </div>

                  <!-- Content -->
                  

                  

                </div>

                <span class="w-[1px] h-auto block bg-[#DDDDDD] max-[768px]:!h-[1px] max-[768px]:w-full"></span>

                <!-- Accordion -->
                <div class="x-home-accordion x-home-accordion--closeOnAny w-full relative px-[30px]">
                  <!-- Header -->
                  <div class="flex items-center h-full gap-[8px] justify-between w-full">
                    <div class="flex items-center gap-[20px] w-full">
                      <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 0.85H0.85V1V21V21.15H1H21H21.15V21V1V0.85H21H14.3333H14.1833V1V2.33333V2.48333H14.3333H19.5167V10.1833H17H16.85V10.3333V11.6667V11.8167H17H19.5167V19.5167H10.4833V11.8167H13H13.15V11.6667V10.3333V10.1833H13H6.33333H6.18333V10.3333V11.6667V11.8167H6.33333H8.85V19.5167H2.48333V2.48333H6.75273L10.547 5.01285L10.6718 5.09605L10.755 4.97124L11.4946 3.86183L11.5778 3.73702L11.453 3.65382L7.28506 0.875192L7.24727 0.85H7.20185H1Z" fill="#717171" stroke="#717171" stroke-width="0.3" />
                      </svg>

                      <select name="category" id="category" class="form-select w-full h-full appearance-none border-none text-black text-[20px] font-bold bg-transparent" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxNCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCA3LjU5NTVMMi4zMzMzMyAxMEw3IDUuMzMzMzNMMTEuNjY2NyAxMEwxNCA3LjU5NTVMNyAwLjY2NjY2NkwwIDcuNTk1NVoiIGZpbGw9ImJsYWNrIi8+PC9zdmc+'); background-position: right 10px center; background-repeat: no-repeat; padding-right: 40px;">
                        <option value=""><?php echo e(__('All Categories')); ?></option>
                        <?php
                        $categories = get_property_categories([
                            'indent' => '↳',
                            'conditions' => ['status' => \Xmetr\Base\Enums\BaseStatusEnum::PUBLISHED]
                        ]);
                        ?>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->slugable->key); ?>" <?php if(request()->query('category') == $category->slugable->key): echo 'selected'; endif; ?>><?php echo e($category->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="h-auto flex items-center pr-[30px] shrink-0 max-[768px]:px-[30px]">
                  <button type="submit" class="bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex justify-center gap-[10px] relative active:scale-[.95] max-[768px]:w-full">
                    <p class="text-white text-[15px] font-bold text-center"><?php echo e($shortcode->btn_txt); ?></p>
                  </button>
                </div>
            </form>

      </div>

    </div>
  </section>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/partials/shortcodes/hero-section/index.blade.php ENDPATH**/ ?>