<?php
    SeoHelper::setTitle(__('410 - Page Not found'));
    Theme::fireEventGlobalAssets();
    SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
?>



<?php $__env->startSection('content'); ?>
    <div class="error-page">
        <h2 class="error-header">410</h2>
        <p class="error-title"><?php echo e(__('Oops… You just found an error page! This page has been removed.')); ?></p>
        <a href="<?php echo e(BaseHelper::getHomepageUrl()); ?>" class="tf-btn primary"><?php echo e(__('Back To Home')); ?></a>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make(Theme::getThemeNamespace('layouts.base'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/410.blade.php ENDPATH**/ ?>