<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Base\Http\Responses\BaseHttpResponse;
use Xmetr\RealEstate\Repositories\Interfaces\ProjectInterface;
use Illuminate\Http\Request;

class ProjectFilterController extends BaseController
{
    public function __construct(protected ProjectInterface $projectRepository)
    {
    }

    public function getProjectsCount(Request $request): BaseHttpResponse
    {
        // Validate the request parameters based on project filters
        $request->validate([
            'keyword' => 'nullable|string|max:255',
            'location' => 'nullable|string',
            'city_id' => 'nullable|numeric',
            'city' => 'nullable|string',
            'state' => 'nullable|string',
            'state_id' => 'nullable|numeric',
            'country_id' => 'nullable|numeric',
            'category_id' => 'nullable|numeric',
            'sort_by' => 'nullable|string',
            'blocks' => 'nullable|numeric',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'min_floor' => 'nullable|numeric',
            'max_floor' => 'nullable|numeric',
            'min_flat' => 'nullable|numeric',
            'max_flat' => 'nullable|numeric',
            'features' => 'nullable|array',
            'features.*' => 'integer',
            'build_class' => 'nullable|string',
        ]);

        // Prepare filters array similar to RealEstateHelper::getProjectsFilter
        $filters = array_merge([
            'keyword' => null,
            'min_floor' => null,
            'max_floor' => null,
            'blocks' => null,
            'min_flat' => null,
            'max_flat' => null,
            'category_id' => null,
            'city_id' => null,
            'city' => null,
            'country_id' => null,
            'min_price' => null,
            'max_price' => null,
            'state' => null,
            'state_id' => null,
            'location' => null,
            'sort_by' => null,
            'features' => null,
            'build_class' => null,
        ], $request->only([
            'min_floor', 'max_floor', 'blocks', 'min_flat', 'max_flat',
            'category_id', 'city_id', 'city', 'country_id', 'min_price', 'max_price',
            'state', 'state_id', 'location', 'sort_by', 'features', 'build_class'
        ]));

        // Handle keyword parameter (mapped from 'k' to 'keyword')
        $filters['keyword'] = $request->input('k');

        // Get count using the same filtering logic as the main projects query
        // but without pagination and with count() instead of get()
        $count = $this->projectRepository->getProjectsCount($filters);

        return $this
            ->httpResponse()
            ->setData(['count' => $count]);
    }
}
