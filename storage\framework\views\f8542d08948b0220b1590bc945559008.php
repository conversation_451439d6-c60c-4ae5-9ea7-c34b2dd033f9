<?php
    Theme::layout('full-width');
?>
<?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.listing'), [
    'actionUrl' => RealEstateHelper::getProjectsListPageUrl(),
    'ajaxUrl' => route('public.projects'),
    'mapUrl' => route('public.ajax.projects.map'),
    'perPages' => RealEstateHelper::getProjectsPerPageList(),
    'itemLayout' => request()->query('layout', 'grid'),
    // 'layout' => theme_option('real_estate_project_listing_layout', 'top-map'),
    'layout' => 'xmetr-projects',
    'filterViewPath' => Theme::getThemeNamespace('views.real-estate.partials.filters.project-search-box'),
    'itemsViewPath' => Theme::getThemeNamespace('views.real-estate.projects.index'),
], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<script>
window.projectCountUrl = '<?php echo e(route('public.ajax.projects.count')); ?>';
</script>

<?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.project-map-content'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/projects.blade.php ENDPATH**/ ?>