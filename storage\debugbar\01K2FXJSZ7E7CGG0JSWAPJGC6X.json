{"__meta": {"id": "01K2FXJSZ7E7CGG0JSWAPJGC6X", "datetime": "2025-08-12 19:54:42", "utime": **********.024307, "method": "POST", "uri": "/admin/real-estate/projects/edit/25", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.143667, "end": **********.024323, "duration": 0.8806560039520264, "duration_str": "881ms", "measures": [{"label": "Booting", "start": **********.143667, "relative_start": 0, "end": **********.884467, "relative_end": **********.884467, "duration": 0.****************, "duration_str": "741ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.884477, "relative_start": 0.****************, "end": **********.024325, "relative_end": 1.9073486328125e-06, "duration": 0.***************, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.900958, "relative_start": 0.****************, "end": **********.917307, "relative_end": **********.917307, "duration": 0.016348838806152344, "duration_str": "16.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.019823, "relative_start": 0.****************, "end": **********.021685, "relative_end": **********.021685, "duration": 0.0018618106842041016, "duration_str": "1.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.015639999999999998, "accumulated_duration_str": "15.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.931255, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 2.302}, {"sql": "select * from `re_projects` where `re_projects`.`id` = '25' limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 103}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9594722, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.302, "width_percent": 3.069}, {"sql": "update `re_projects` set `build_class` = 'business', `status` = 'published', `re_projects`.`updated_at` = '2025-08-12 19:54:41' where `id` = 25", "type": "query", "params": [], "bindings": [{"value": "business", "label": "Business"}, {"value": "published", "label": "Published"}, "2025-08-12 19:54:41", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 111}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9716098, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "ProjectController.php:111", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 111}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FProjectController.php:111", "ajax": false, "filename": "ProjectController.php", "line": "111"}, "connection": "xmetr", "explain": null, "start_percent": 5.371, "width_percent": 27.302}, {"sql": "select * from `re_project_features` where `re_project_features`.`project_id` = 25", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.984036, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "ProjectController.php:117", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 117}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FProjectController.php:117", "ajax": false, "filename": "ProjectController.php", "line": "117"}, "connection": "xmetr", "explain": null, "start_percent": 32.673, "width_percent": 23.465}, {"sql": "delete from `re_facilities_distances` where `re_facilities_distances`.`reference_id` = 25 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project'", "type": "query", "params": [], "bindings": [25, "Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 119}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9908671, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "SaveFacilitiesService.php:12", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FSaveFacilitiesService.php:12", "ajax": false, "filename": "SaveFacilitiesService.php", "line": "12"}, "connection": "xmetr", "explain": null, "start_percent": 56.138, "width_percent": 6.266}, {"sql": "delete from `re_project_categories` where `re_project_categories`.`project_id` = 25", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StoreProjectCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StoreProjectCategoryService.php", "line": 18}, {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 121}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9934058, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "StoreProjectCategoryService.php:18", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StoreProjectCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StoreProjectCategoryService.php", "line": 18}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FStoreProjectCategoryService.php:18", "ajax": false, "filename": "StoreProjectCategoryService.php", "line": "18"}, "connection": "xmetr", "explain": null, "start_percent": 62.404, "width_percent": 4.987}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 25 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 25, "Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.998266, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 67.391, "width_percent": 2.174}, {"sql": "select * from `slugs` where (`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project' and `reference_id` = 25) limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Project", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.003351, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.565, "width_percent": 1.471}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 889}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\UpdatedContentListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.0062988, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 71.036, "width_percent": 2.749}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'project', 'updated', 1, 1, 25, 'Castle View', 'primary', '2025-08-12 19:54:42', '2025-08-12 19:54:42', '{\\\"name\\\":\\\"Castle View\\\",\\\"model\\\":\\\"Xmetr\\\\\\\\RealEstate\\\\\\\\Models\\\\\\\\Project\\\",\\\"slug\\\":\\\"castle-view\\\",\\\"slug_id\\\":\\\"205\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"description\\\":null,\\\"is_featured\\\":\\\"0\\\",\\\"content\\\":\\\"<p>\\\\u0416\\\\u0438\\\\u043b\\\\u043e\\\\u0439 \\\\u043a\\\\u043e\\\\u043c\\\\u043f\\\\u043b\\\\u0435\\\\u043a\\\\u0441 <strong>Castle View<\\\\/strong>, \\\\u0440\\\\u0430\\\\u0441\\\\u043f\\\\u043e\\\\u043b\\\\u043e\\\\u0436\\\\u0435\\\\u043d\\\\u043d\\\\u044b\\\\u0439 \\\\u043f\\\\u043e \\\\u0430\\\\u0434\\\\u0440\\\\u0435\\\\u0441\\\\u0443 <strong>Luis Maria Campos 1000<\\\\/strong> \\\\u0432 \\\\u0411\\\\u0443\\\\u044d\\\\u043d\\\\u043e\\\\u0441-\\\\u0410\\\\u0439\\\\u0440\\\\u0435\\\\u0441\\\\u0435, \\\\u043f\\\\u0440\\\\u0435\\\\u0434\\\\u0441\\\\u0442\\\\u0430\\\\u0432\\\\u043b\\\\u044f\\\\u0435\\\\u0442 \\\\u0441\\\\u043e\\\\u0431\\\\u043e\\\\u0439 \\\\u0438\\\\u0434\\\\u0435\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u043e\\\\u0435 \\\\u0441\\\\u043e\\\\u0447\\\\u0435\\\\u0442\\\\u0430\\\\u043d\\\\u0438\\\\u0435 \\\\u0441\\\\u043e\\\\u0432\\\\u0440\\\\u0435\\\\u043c\\\\u0435\\\\u043d\\\\u043d\\\\u043e\\\\u0433\\\\u043e \\\\u043a\\\\u043e\\\\u043c\\\\u0444\\\\u043e\\\\u0440\\\\u0442\\\\u0430 \\\\u0438 \\\\u0443\\\\u0434\\\\u043e\\\\u0431\\\\u043d\\\\u043e\\\\u0433\\\\u043e \\\\u0440\\\\u0430\\\\u0441\\\\u043f\\\\u043e\\\\u043b\\\\u043e\\\\u0436\\\\u0435\\\\u043d\\\\u0438\\\\u044f \\\\u0432 \\\\u043f\\\\u0440\\\\u0435\\\\u0441\\\\u0442\\\\u0438\\\\u0436\\\\u043d\\\\u043e\\\\u043c \\\\u0440\\\\u0430\\\\u0439\\\\u043e\\\\u043d\\\\u0435 \\\\u0433\\\\u043e\\\\u0440\\\\u043e\\\\u0434\\\\u0430. \\\\u0417\\\\u0434\\\\u0430\\\\u043d\\\\u0438\\\\u0435 \\\\u043e\\\\u043a\\\\u0440\\\\u0443\\\\u0436\\\\u0435\\\\u043d\\\\u043e \\\\u043a\\\\u0440\\\\u0430\\\\u0441\\\\u0438\\\\u0432\\\\u044b\\\\u043c\\\\u0438 \\\\u0437\\\\u0435\\\\u043b\\\\u0435\\\\u043d\\\\u044b\\\\u043c\\\\u0438 \\\\u043f\\\\u0430\\\\u0440\\\\u043a\\\\u0430\\\\u043c\\\\u0438, \\\\u0432\\\\u043a\\\\u043b\\\\u044e\\\\u0447\\\\u0430\\\\u044f <strong>Parque Las Heras<\\\\/strong>, \\\\u043a\\\\u043e\\\\u0442\\\\u043e\\\\u0440\\\\u044b\\\\u0439 \\\\u043d\\\\u0430\\\\u0445\\\\u043e\\\\u0434\\\\u0438\\\\u0442\\\\u0441\\\\u044f \\\\u0432\\\\u0441\\\\u0435\\\\u0433\\\\u043e \\\\u0432 \\\\u043d\\\\u0435\\\\u0441\\\\u043a\\\\u043e\\\\u043b\\\\u044c\\\\u043a\\\\u0438\\\\u0445 \\\\u043c\\\\u0438\\\\u043d\\\\u0443\\\\u0442\\\\u0430\\\\u0445 \\\\u0445\\\\u043e\\\\u0434\\\\u044c\\\\u0431\\\\u044b. \\\\u042d\\\\u0442\\\\u043e \\\\u043e\\\\u0442\\\\u043b\\\\u0438\\\\u0447\\\\u043d\\\\u043e\\\\u0435 \\\\u043c\\\\u0435\\\\u0441\\\\u0442\\\\u043e \\\\u0434\\\\u043b\\\\u044f \\\\u043f\\\\u0440\\\\u043e\\\\u0433\\\\u0443\\\\u043b\\\\u043e\\\\u043a, \\\\u0437\\\\u0430\\\\u043d\\\\u044f\\\\u0442\\\\u0438\\\\u0439 \\\\u0441\\\\u043f\\\\u043e\\\\u0440\\\\u0442\\\\u043e\\\\u043c \\\\u0438 \\\\u043e\\\\u0442\\\\u0434\\\\u044b\\\\u0445\\\\u0430 \\\\u043d\\\\u0430 \\\\u0441\\\\u0432\\\\u0435\\\\u0436\\\\u0435\\\\u043c \\\\u0432\\\\u043e\\\\u0437\\\\u0434\\\\u0443\\\\u0445\\\\u0435.<\\\\/p><p>\\\\u0412\\\\u0431\\\\u043b\\\\u0438\\\\u0437\\\\u0438 \\\\u043a\\\\u043e\\\\u043c\\\\u043f\\\\u043b\\\\u0435\\\\u043a\\\\u0441\\\\u0430 \\\\u0440\\\\u0430\\\\u0441\\\\u043f\\\\u043e\\\\u043b\\\\u043e\\\\u0436\\\\u0435\\\\u043d\\\\u044b <strong>\\\\u0441\\\\u0443\\\\u043f\\\\u0435\\\\u0440\\\\u043c\\\\u0430\\\\u0440\\\\u043a\\\\u0435\\\\u0442\\\\u044b, \\\\u043c\\\\u0430\\\\u0433\\\\u0430\\\\u0437\\\\u0438\\\\u043d\\\\u044b, \\\\u043a\\\\u0430\\\\u0444\\\\u0435 \\\\u0438 \\\\u0440\\\\u0435\\\\u0441\\\\u0442\\\\u043e\\\\u0440\\\\u0430\\\\u043d\\\\u044b<\\\\/strong>, \\\\u0447\\\\u0442\\\\u043e \\\\u0434\\\\u0435\\\\u043b\\\\u0430\\\\u0435\\\\u0442 \\\\u043f\\\\u043e\\\\u0432\\\\u0441\\\\u0435\\\\u0434\\\\u043d\\\\u0435\\\\u0432\\\\u043d\\\\u0443\\\\u044e \\\\u0436\\\\u0438\\\\u0437\\\\u043d\\\\u044c \\\\u0443\\\\u0434\\\\u043e\\\\u0431\\\\u043d\\\\u043e\\\\u0439 \\\\u0438 \\\\u043d\\\\u0430\\\\u0441\\\\u044b\\\\u0449\\\\u0435\\\\u043d\\\\u043d\\\\u043e\\\\u0439. \\\\u0422\\\\u0430\\\\u043a\\\\u0436\\\\u0435 \\\\u0432 \\\\u043d\\\\u0435\\\\u0441\\\\u043a\\\\u043e\\\\u043b\\\\u044c\\\\u043a\\\\u0438\\\\u0445 \\\\u043a\\\\u0432\\\\u0430\\\\u0440\\\\u0442\\\\u0430\\\\u043b\\\\u0430\\\\u0445 \\\\u043d\\\\u0430\\\\u0445\\\\u043e\\\\u0434\\\\u044f\\\\u0442\\\\u0441\\\\u044f <strong>\\\\u043e\\\\u0431\\\\u0440\\\\u0430\\\\u0437\\\\u043e\\\\u0432\\\\u0430\\\\u0442\\\\u0435\\\\u043b\\\\u044c\\\\u043d\\\\u044b\\\\u0435 \\\\u0443\\\\u0447\\\\u0440\\\\u0435\\\\u0436\\\\u0434\\\\u0435\\\\u043d\\\\u0438\\\\u044f<\\\\/strong>, \\\\u0432\\\\u043a\\\\u043b\\\\u044e\\\\u0447\\\\u0430\\\\u044f \\\\u0448\\\\u043a\\\\u043e\\\\u043b\\\\u044b \\\\u0438 \\\\u0434\\\\u0435\\\\u0442\\\\u0441\\\\u043a\\\\u0438\\\\u0435 \\\\u0441\\\\u0430\\\\u0434\\\\u044b, \\\\u0447\\\\u0442\\\\u043e \\\\u043e\\\\u0441\\\\u043e\\\\u0431\\\\u0435\\\\u043d\\\\u043d\\\\u043e \\\\u0432\\\\u0430\\\\u0436\\\\u043d\\\\u043e \\\\u0434\\\\u043b\\\\u044f \\\\u0441\\\\u0435\\\\u043c\\\\u0435\\\\u0439 \\\\u0441 \\\\u0434\\\\u0435\\\\u0442\\\\u044c\\\\u043c\\\\u0438.<\\\\/p><p>\\\\u0411\\\\u043b\\\\u0430\\\\u0433\\\\u043e\\\\u0434\\\\u0430\\\\u0440\\\\u044f \\\\u0431\\\\u043b\\\\u0438\\\\u0437\\\\u043e\\\\u0441\\\\u0442\\\\u0438 \\\\u043a \\\\u0433\\\\u043b\\\\u0430\\\\u0432\\\\u043d\\\\u044b\\\\u043c \\\\u0442\\\\u0440\\\\u0430\\\\u043d\\\\u0441\\\\u043f\\\\u043e\\\\u0440\\\\u0442\\\\u043d\\\\u044b\\\\u043c \\\\u0443\\\\u0437\\\\u043b\\\\u0430\\\\u043c, <strong>Castle View<\\\\/strong> \\\\u043e\\\\u0431\\\\u0435\\\\u0441\\\\u043f\\\\u0435\\\\u0447\\\\u0438\\\\u0432\\\\u0430\\\\u0435\\\\u0442 \\\\u043b\\\\u0435\\\\u0433\\\\u043a\\\\u0438\\\\u0439 \\\\u0434\\\\u043e\\\\u0441\\\\u0442\\\\u0443\\\\u043f \\\\u043a \\\\u0440\\\\u0430\\\\u0437\\\\u043b\\\\u0438\\\\u0447\\\\u043d\\\\u044b\\\\u043c \\\\u0447\\\\u0430\\\\u0441\\\\u0442\\\\u044f\\\\u043c \\\\u0433\\\\u043e\\\\u0440\\\\u043e\\\\u0434\\\\u0430. \\\\u0412 \\\\u043f\\\\u0435\\\\u0448\\\\u0435\\\\u0439 \\\\u0434\\\\u043e\\\\u0441\\\\u0442\\\\u0443\\\\u043f\\\\u043d\\\\u043e\\\\u0441\\\\u0442\\\\u0438 \\\\u043d\\\\u0430\\\\u0445\\\\u043e\\\\u0434\\\\u044f\\\\u0442\\\\u0441\\\\u044f \\\\u0430\\\\u0432\\\\u0442\\\\u043e\\\\u0431\\\\u0443\\\\u0441\\\\u043d\\\\u044b\\\\u0435 \\\\u043e\\\\u0441\\\\u0442\\\\u0430\\\\u043d\\\\u043e\\\\u0432\\\\u043a\\\\u0438 \\\\u0438 \\\\u0441\\\\u0442\\\\u0430\\\\u043d\\\\u0446\\\\u0438\\\\u044f \\\\u043c\\\\u0435\\\\u0442\\\\u0440\\\\u043e, \\\\u0430 \\\\u0442\\\\u0430\\\\u043a\\\\u0436\\\\u0435 <strong>\\\\u0436\\\\u0435\\\\u043b\\\\u0435\\\\u0437\\\\u043d\\\\u043e\\\\u0434\\\\u043e\\\\u0440\\\\u043e\\\\u0436\\\\u043d\\\\u0430\\\\u044f \\\\u0441\\\\u0442\\\\u0430\\\\u043d\\\\u0446\\\\u0438\\\\u044f<\\\\/strong> Belgrano C, \\\\u0447\\\\u0442\\\\u043e \\\\u043f\\\\u043e\\\\u0437\\\\u0432\\\\u043e\\\\u043b\\\\u044f\\\\u0435\\\\u0442 \\\\u0431\\\\u044b\\\\u0441\\\\u0442\\\\u0440\\\\u043e \\\\u0434\\\\u043e\\\\u0431\\\\u0440\\\\u0430\\\\u0442\\\\u044c\\\\u0441\\\\u044f \\\\u0434\\\\u043e \\\\u0434\\\\u0435\\\\u043b\\\\u043e\\\\u0432\\\\u043e\\\\u0433\\\\u043e \\\\u0446\\\\u0435\\\\u043d\\\\u0442\\\\u0440\\\\u0430 \\\\u0438\\\\u043b\\\\u0438 \\\\u0434\\\\u0440\\\\u0443\\\\u0433\\\\u0438\\\\u0445 \\\\u0440\\\\u0430\\\\u0439\\\\u043e\\\\u043d\\\\u043e\\\\u0432 \\\\u0411\\\\u0443\\\\u044d\\\\u043d\\\\u043e\\\\u0441-\\\\u0410\\\\u0439\\\\u0440\\\\u0435\\\\u0441\\\\u0430.<\\\\/p><p>\\\\u0416\\\\u0438\\\\u043b\\\\u043e\\\\u0439 \\\\u043a\\\\u043e\\\\u043c\\\\u043f\\\\u043b\\\\u0435\\\\u043a\\\\u0441 \\\\u0442\\\\u0430\\\\u043a\\\\u0436\\\\u0435 \\\\u043f\\\\u0440\\\\u0435\\\\u0434\\\\u043b\\\\u0430\\\\u0433\\\\u0430\\\\u0435\\\\u0442 <strong>\\\\u0432\\\\u044b\\\\u0441\\\\u043e\\\\u043a\\\\u0438\\\\u0439 \\\\u0443\\\\u0440\\\\u043e\\\\u0432\\\\u0435\\\\u043d\\\\u044c \\\\u0431\\\\u0435\\\\u0437\\\\u043e\\\\u043f\\\\u0430\\\\u0441\\\\u043d\\\\u043e\\\\u0441\\\\u0442\\\\u0438<\\\\/strong> \\\\u0438 \\\\u0441\\\\u043e\\\\u0432\\\\u0440\\\\u0435\\\\u043c\\\\u0435\\\\u043d\\\\u043d\\\\u0443\\\\u044e \\\\u0438\\\\u043d\\\\u0444\\\\u0440\\\\u0430\\\\u0441\\\\u0442\\\\u0440\\\\u0443\\\\u043a\\\\u0442\\\\u0443\\\\u0440\\\\u0443: \\\\u043a\\\\u0440\\\\u0443\\\\u0433\\\\u043b\\\\u043e\\\\u0441\\\\u0443\\\\u0442\\\\u043e\\\\u0447\\\\u043d\\\\u0443\\\\u044e \\\\u043e\\\\u0445\\\\u0440\\\\u0430\\\\u043d\\\\u0443, \\\\u043f\\\\u0430\\\\u0440\\\\u043a\\\\u043e\\\\u0432\\\\u043a\\\\u0443, \\\\u0442\\\\u0440\\\\u0435\\\\u043d\\\\u0430\\\\u0436\\\\u0435\\\\u0440\\\\u043d\\\\u044b\\\\u0439 \\\\u0437\\\\u0430\\\\u043b \\\\u0438 \\\\u0431\\\\u0430\\\\u0441\\\\u0441\\\\u0435\\\\u0439\\\\u043d.<\\\\/p>\\\",\\\"images\\\":[null,\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-1.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-9.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-7.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-3.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-10.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-11-1.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-8-1.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-6-1.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-4-1.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-5-1.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-12.jpg\\\",\\\"properties\\\\/gk-castle-view\\\\/gk-buenos-aires-castle-view-2.jpg\\\"],\\\"video\\\":null,\\\"country_id\\\":\\\"22\\\",\\\"state_id\\\":null,\\\"city_id\\\":\\\"8175\\\",\\\"district_id\\\":null,\\\"location\\\":\\\"Luis Maria Campos 1000\\\",\\\"latitude\\\":\\\"-34.5710205\\\",\\\"longitude\\\":\\\"-58.4373146\\\",\\\"number_block\\\":null,\\\"number_floor\\\":null,\\\"number_flat\\\":null,\\\"parking\\\":null,\\\"year_built\\\":null,\\\"build_class\\\":\\\"business\\\",\\\"features\\\":[\\\"1\\\"],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"apply\\\",\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"published\\\",\\\"unique_id\\\":null,\\\"author_id\\\":null}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "project", "updated", 1, 1, 25, "Castle View", "primary", "2025-08-12 19:54:42", "2025-08-12 19:54:42", "{\"name\":\"Castle View\",\"model\":\"Xmetr\\\\RealEstate\\\\Models\\\\Project\",\"slug\":\"castle-view\",\"slug_id\":\"205\",\"is_slug_editable\":\"1\",\"description\":null,\"is_featured\":\"0\",\"content\":\"<p>\\u0416\\u0438\\u043b\\u043e\\u0439 \\u043a\\u043e\\u043c\\u043f\\u043b\\u0435\\u043a\\u0441 <strong>Castle View<\\/strong>, \\u0440\\u0430\\u0441\\u043f\\u043e\\u043b\\u043e\\u0436\\u0435\\u043d\\u043d\\u044b\\u0439 \\u043f\\u043e \\u0430\\u0434\\u0440\\u0435\\u0441\\u0443 <strong>Luis <PERSON> 1000<\\/strong> \\u0432 \\u0411\\u0443\\u044d\\u043d\\u043e\\u0441-\\u0410\\u0439\\u0440\\u0435\\u0441\\u0435, \\u043f\\u0440\\u0435\\u0434\\u0441\\u0442\\u0430\\u0432\\u043b\\u044f\\u0435\\u0442 \\u0441\\u043e\\u0431\\u043e\\u0439 \\u0438\\u0434\\u0435\\u0430\\u043b\\u044c\\u043d\\u043e\\u0435 \\u0441\\u043e\\u0447\\u0435\\u0442\\u0430\\u043d\\u0438\\u0435 \\u0441\\u043e\\u0432\\u0440\\u0435\\u043c\\u0435\\u043d\\u043d\\u043e\\u0433\\u043e \\u043a\\u043e\\u043c\\u0444\\u043e\\u0440\\u0442\\u0430 \\u0438 \\u0443\\u0434\\u043e\\u0431\\u043d\\u043e\\u0433\\u043e \\u0440\\u0430\\u0441\\u043f\\u043e\\u043b\\u043e\\u0436\\u0435\\u043d\\u0438\\u044f \\u0432 \\u043f\\u0440\\u0435\\u0441\\u0442\\u0438\\u0436\\u043d\\u043e\\u043c \\u0440\\u0430\\u0439\\u043e\\u043d\\u0435 \\u0433\\u043e\\u0440\\u043e\\u0434\\u0430. \\u0417\\u0434\\u0430\\u043d\\u0438\\u0435 \\u043e\\u043a\\u0440\\u0443\\u0436\\u0435\\u043d\\u043e \\u043a\\u0440\\u0430\\u0441\\u0438\\u0432\\u044b\\u043c\\u0438 \\u0437\\u0435\\u043b\\u0435\\u043d\\u044b\\u043c\\u0438 \\u043f\\u0430\\u0440\\u043a\\u0430\\u043c\\u0438, \\u0432\\u043a\\u043b\\u044e\\u0447\\u0430\\u044f <strong>Parque Las Heras<\\/strong>, \\u043a\\u043e\\u0442\\u043e\\u0440\\u044b\\u0439 \\u043d\\u0430\\u0445\\u043e\\u0434\\u0438\\u0442\\u0441\\u044f \\u0432\\u0441\\u0435\\u0433\\u043e \\u0432 \\u043d\\u0435\\u0441\\u043a\\u043e\\u043b\\u044c\\u043a\\u0438\\u0445 \\u043c\\u0438\\u043d\\u0443\\u0442\\u0430\\u0445 \\u0445\\u043e\\u0434\\u044c\\u0431\\u044b. \\u042d\\u0442\\u043e \\u043e\\u0442\\u043b\\u0438\\u0447\\u043d\\u043e\\u0435 \\u043c\\u0435\\u0441\\u0442\\u043e \\u0434\\u043b\\u044f \\u043f\\u0440\\u043e\\u0433\\u0443\\u043b\\u043e\\u043a, \\u0437\\u0430\\u043d\\u044f\\u0442\\u0438\\u0439 \\u0441\\u043f\\u043e\\u0440\\u0442\\u043e\\u043c \\u0438 \\u043e\\u0442\\u0434\\u044b\\u0445\\u0430 \\u043d\\u0430 \\u0441\\u0432\\u0435\\u0436\\u0435\\u043c \\u0432\\u043e\\u0437\\u0434\\u0443\\u0445\\u0435.<\\/p><p>\\u0412\\u0431\\u043b\\u0438\\u0437\\u0438 \\u043a\\u043e\\u043c\\u043f\\u043b\\u0435\\u043a\\u0441\\u0430 \\u0440\\u0430\\u0441\\u043f\\u043e\\u043b\\u043e\\u0436\\u0435\\u043d\\u044b <strong>\\u0441\\u0443\\u043f\\u0435\\u0440\\u043c\\u0430\\u0440\\u043a\\u0435\\u0442\\u044b, \\u043c\\u0430\\u0433\\u0430\\u0437\\u0438\\u043d\\u044b, \\u043a\\u0430\\u0444\\u0435 \\u0438 \\u0440\\u0435\\u0441\\u0442\\u043e\\u0440\\u0430\\u043d\\u044b<\\/strong>, \\u0447\\u0442\\u043e \\u0434\\u0435\\u043b\\u0430\\u0435\\u0442 \\u043f\\u043e\\u0432\\u0441\\u0435\\u0434\\u043d\\u0435\\u0432\\u043d\\u0443\\u044e \\u0436\\u0438\\u0437\\u043d\\u044c \\u0443\\u0434\\u043e\\u0431\\u043d\\u043e\\u0439 \\u0438 \\u043d\\u0430\\u0441\\u044b\\u0449\\u0435\\u043d\\u043d\\u043e\\u0439. \\u0422\\u0430\\u043a\\u0436\\u0435 \\u0432 \\u043d\\u0435\\u0441\\u043a\\u043e\\u043b\\u044c\\u043a\\u0438\\u0445 \\u043a\\u0432\\u0430\\u0440\\u0442\\u0430\\u043b\\u0430\\u0445 \\u043d\\u0430\\u0445\\u043e\\u0434\\u044f\\u0442\\u0441\\u044f <strong>\\u043e\\u0431\\u0440\\u0430\\u0437\\u043e\\u0432\\u0430\\u0442\\u0435\\u043b\\u044c\\u043d\\u044b\\u0435 \\u0443\\u0447\\u0440\\u0435\\u0436\\u0434\\u0435\\u043d\\u0438\\u044f<\\/strong>, \\u0432\\u043a\\u043b\\u044e\\u0447\\u0430\\u044f \\u0448\\u043a\\u043e\\u043b\\u044b \\u0438 \\u0434\\u0435\\u0442\\u0441\\u043a\\u0438\\u0435 \\u0441\\u0430\\u0434\\u044b, \\u0447\\u0442\\u043e \\u043e\\u0441\\u043e\\u0431\\u0435\\u043d\\u043d\\u043e \\u0432\\u0430\\u0436\\u043d\\u043e \\u0434\\u043b\\u044f \\u0441\\u0435\\u043c\\u0435\\u0439 \\u0441 \\u0434\\u0435\\u0442\\u044c\\u043c\\u0438.<\\/p><p>\\u0411\\u043b\\u0430\\u0433\\u043e\\u0434\\u0430\\u0440\\u044f \\u0431\\u043b\\u0438\\u0437\\u043e\\u0441\\u0442\\u0438 \\u043a \\u0433\\u043b\\u0430\\u0432\\u043d\\u044b\\u043c \\u0442\\u0440\\u0430\\u043d\\u0441\\u043f\\u043e\\u0440\\u0442\\u043d\\u044b\\u043c \\u0443\\u0437\\u043b\\u0430\\u043c, <strong>Castle View<\\/strong> \\u043e\\u0431\\u0435\\u0441\\u043f\\u0435\\u0447\\u0438\\u0432\\u0430\\u0435\\u0442 \\u043b\\u0435\\u0433\\u043a\\u0438\\u0439 \\u0434\\u043e\\u0441\\u0442\\u0443\\u043f \\u043a \\u0440\\u0430\\u0437\\u043b\\u0438\\u0447\\u043d\\u044b\\u043c \\u0447\\u0430\\u0441\\u0442\\u044f\\u043c \\u0433\\u043e\\u0440\\u043e\\u0434\\u0430. \\u0412 \\u043f\\u0435\\u0448\\u0435\\u0439 \\u0434\\u043e\\u0441\\u0442\\u0443\\u043f\\u043d\\u043e\\u0441\\u0442\\u0438 \\u043d\\u0430\\u0445\\u043e\\u0434\\u044f\\u0442\\u0441\\u044f \\u0430\\u0432\\u0442\\u043e\\u0431\\u0443\\u0441\\u043d\\u044b\\u0435 \\u043e\\u0441\\u0442\\u0430\\u043d\\u043e\\u0432\\u043a\\u0438 \\u0438 \\u0441\\u0442\\u0430\\u043d\\u0446\\u0438\\u044f \\u043c\\u0435\\u0442\\u0440\\u043e, \\u0430 \\u0442\\u0430\\u043a\\u0436\\u0435 <strong>\\u0436\\u0435\\u043b\\u0435\\u0437\\u043d\\u043e\\u0434\\u043e\\u0440\\u043e\\u0436\\u043d\\u0430\\u044f \\u0441\\u0442\\u0430\\u043d\\u0446\\u0438\\u044f<\\/strong> Belgrano C, \\u0447\\u0442\\u043e \\u043f\\u043e\\u0437\\u0432\\u043e\\u043b\\u044f\\u0435\\u0442 \\u0431\\u044b\\u0441\\u0442\\u0440\\u043e \\u0434\\u043e\\u0431\\u0440\\u0430\\u0442\\u044c\\u0441\\u044f \\u0434\\u043e \\u0434\\u0435\\u043b\\u043e\\u0432\\u043e\\u0433\\u043e \\u0446\\u0435\\u043d\\u0442\\u0440\\u0430 \\u0438\\u043b\\u0438 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445 \\u0440\\u0430\\u0439\\u043e\\u043d\\u043e\\u0432 \\u0411\\u0443\\u044d\\u043d\\u043e\\u0441-\\u0410\\u0439\\u0440\\u0435\\u0441\\u0430.<\\/p><p>\\u0416\\u0438\\u043b\\u043e\\u0439 \\u043a\\u043e\\u043c\\u043f\\u043b\\u0435\\u043a\\u0441 \\u0442\\u0430\\u043a\\u0436\\u0435 \\u043f\\u0440\\u0435\\u0434\\u043b\\u0430\\u0433\\u0430\\u0435\\u0442 <strong>\\u0432\\u044b\\u0441\\u043e\\u043a\\u0438\\u0439 \\u0443\\u0440\\u043e\\u0432\\u0435\\u043d\\u044c \\u0431\\u0435\\u0437\\u043e\\u043f\\u0430\\u0441\\u043d\\u043e\\u0441\\u0442\\u0438<\\/strong> \\u0438 \\u0441\\u043e\\u0432\\u0440\\u0435\\u043c\\u0435\\u043d\\u043d\\u0443\\u044e \\u0438\\u043d\\u0444\\u0440\\u0430\\u0441\\u0442\\u0440\\u0443\\u043a\\u0442\\u0443\\u0440\\u0443: \\u043a\\u0440\\u0443\\u0433\\u043b\\u043e\\u0441\\u0443\\u0442\\u043e\\u0447\\u043d\\u0443\\u044e \\u043e\\u0445\\u0440\\u0430\\u043d\\u0443, \\u043f\\u0430\\u0440\\u043a\\u043e\\u0432\\u043a\\u0443, \\u0442\\u0440\\u0435\\u043d\\u0430\\u0436\\u0435\\u0440\\u043d\\u044b\\u0439 \\u0437\\u0430\\u043b \\u0438 \\u0431\\u0430\\u0441\\u0441\\u0435\\u0439\\u043d.<\\/p>\",\"images\":[null,\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-1.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-9.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-7.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-3.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-10.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-11-1.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-8-1.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-6-1.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-4-1.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-5-1.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-12.jpg\",\"properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-2.jpg\"],\"video\":null,\"country_id\":\"22\",\"state_id\":null,\"city_id\":\"8175\",\"district_id\":null,\"location\":\"Luis Maria Campos 1000\",\"latitude\":\"-34.5710205\",\"longitude\":\"-58.4373146\",\"number_block\":null,\"number_floor\":null,\"number_flat\":null,\"parking\":null,\"year_built\":null,\"build_class\":\"business\",\"features\":[\"1\"],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"apply\",\"language\":\"en_US\",\"status\":\"published\",\"unique_id\":null,\"author_id\":null}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.012645, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 73.785, "width_percent": 26.215}]}, "models": {"data": {"Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Project": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProject.php:1", "ajax": false, "filename": "Project.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/projects/edit/25", "action_name": "project.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\ProjectController@update", "uri": "POST admin/real-estate/projects/edit/{project}", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\ProjectController@update<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FProjectController.php:94\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers", "prefix": "admin/real-estate/projects", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FProjectController.php:94\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/ProjectController.php:94-129</a>", "middleware": "web, core, auth", "duration": "879ms", "peak_memory": "52MB", "response": "Redirect to https://xmetr.gc/admin/real-estate/projects/edit/25", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-354603603 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-354603603\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-256279108 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Castle View</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">castle-view</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">205</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"1286 characters\">&lt;p&gt;&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089; &lt;strong&gt;Castle View&lt;/strong&gt;, &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1085;&#1099;&#1081; &#1087;&#1086; &#1072;&#1076;&#1088;&#1077;&#1089;&#1091; &lt;strong&gt;Luis Maria Campos 1000&lt;/strong&gt; &#1074; &#1041;&#1091;&#1101;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;&#1077;, &#1087;&#1088;&#1077;&#1076;&#1089;&#1090;&#1072;&#1074;&#1083;&#1103;&#1077;&#1090; &#1089;&#1086;&#1073;&#1086;&#1081; &#1080;&#1076;&#1077;&#1072;&#1083;&#1100;&#1085;&#1086;&#1077; &#1089;&#1086;&#1095;&#1077;&#1090;&#1072;&#1085;&#1080;&#1077; &#1089;&#1086;&#1074;&#1088;&#1077;&#1084;&#1077;&#1085;&#1085;&#1086;&#1075;&#1086; &#1082;&#1086;&#1084;&#1092;&#1086;&#1088;&#1090;&#1072; &#1080; &#1091;&#1076;&#1086;&#1073;&#1085;&#1086;&#1075;&#1086; &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1080;&#1103; &#1074; &#1087;&#1088;&#1077;&#1089;&#1090;&#1080;&#1078;&#1085;&#1086;&#1084; &#1088;&#1072;&#1081;&#1086;&#1085;&#1077; &#1075;&#1086;&#1088;&#1086;&#1076;&#1072;. &#1047;&#1076;&#1072;&#1085;&#1080;&#1077; &#1086;&#1082;&#1088;&#1091;&#1078;&#1077;&#1085;&#1086; &#1082;&#1088;&#1072;&#1089;&#1080;&#1074;&#1099;&#1084;&#1080; &#1079;&#1077;&#1083;&#1077;&#1085;&#1099;&#1084;&#1080; &#1087;&#1072;&#1088;&#1082;&#1072;&#1084;&#1080;, &#1074;&#1082;&#1083;&#1102;&#1095;&#1072;&#1103; &lt;strong&gt;Parque Las Heras&lt;/strong&gt;, &#1082;&#1086;&#1090;&#1086;&#1088;&#1099;&#1081; &#1085;&#1072;&#1093;&#1086;&#1076;&#1080;&#1090;&#1089;&#1103; &#1074;&#1089;&#1077;&#1075;&#1086; &#1074; &#1085;&#1077;&#1089;&#1082;&#1086;&#1083;&#1100;&#1082;&#1080;&#1093; &#1084;&#1080;&#1085;&#1091;&#1090;&#1072;&#1093; &#1093;&#1086;&#1076;&#1100;&#1073;&#1099;. &#1069;&#1090;&#1086; &#1086;&#1090;&#1083;&#1080;&#1095;&#1085;&#1086;&#1077; &#1084;&#1077;&#1089;&#1090;&#1086; &#1076;&#1083;&#1103; &#1087;&#1088;&#1086;&#1075;&#1091;&#1083;&#1086;&#1082;, &#1079;&#1072;&#1085;&#1103;&#1090;&#1080;&#1081; &#1089;&#1087;&#1086;&#1088;&#1090;&#1086;&#1084; &#1080; &#1086;&#1090;&#1076;&#1099;&#1093;&#1072; &#1085;&#1072; &#1089;&#1074;&#1077;&#1078;&#1077;&#1084; &#1074;&#1086;&#1079;&#1076;&#1091;&#1093;&#1077;.&lt;/p&gt;&lt;p&gt;&#1042;&#1073;&#1083;&#1080;&#1079;&#1080; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089;&#1072; &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1099; &lt;strong&gt;&#1089;&#1091;&#1087;&#1077;&#1088;&#1084;&#1072;&#1088;&#1082;&#1077;&#1090;&#1099;, &#1084;&#1072;&#1075;&#1072;&#1079;&#1080;&#1085;&#1099;, &#1082;&#1072;&#1092;&#1077; &#1080; &#1088;&#1077;&#1089;&#1090;&#1086;&#1088;&#1072;&#1085;&#1099;&lt;/strong&gt;, &#1095;&#1090;&#1086; &#1076;&#1077;&#1083;&#1072;&#1077;&#1090; &#1087;&#1086;&#1074;&#1089;&#1077;&#1076;&#1085;&#1077;&#1074;&#1085;&#1091;&#1102; &#1078;&#1080;&#1079;&#1085;&#1100; &#1091;&#1076;&#1086;&#1073;&#1085;&#1086;&#1081; &#1080; &#1085;&#1072;&#1089;&#1099;&#1097;&#1077;&#1085;&#1085;&#1086;&#1081;. &#1058;&#1072;&#1082;&#1078;&#1077; &#1074; &#1085;&#1077;&#1089;&#1082;&#1086;&#1083;&#1100;&#1082;&#1080;&#1093; &#1082;&#1074;&#1072;&#1088;&#1090;&#1072;&#1083;&#1072;&#1093; &#1085;&#1072;&#1093;&#1086;&#1076;&#1103;&#1090;&#1089;&#1103; &lt;strong&gt;&#1086;&#1073;&#1088;&#1072;&#1079;&#1086;&#1074;&#1072;&#1090;&#1077;&#1083;&#1100;&#1085;&#1099;&#1077; &#1091;&#1095;&#1088;&#1077;&#1078;&#1076;&#1077;&#1085;&#1080;&#1103;&lt;/strong&gt;, &#1074;&#1082;&#1083;&#1102;&#1095;&#1072;&#1103; &#1096;&#1082;&#1086;&#1083;&#1099; &#1080; &#1076;&#1077;&#1090;&#1089;&#1082;&#1080;&#1077; &#1089;&#1072;&#1076;&#1099;, &#1095;&#1090;&#1086; &#1086;&#1089;&#1086;&#1073;&#1077;&#1085;&#1085;&#1086; &#1074;&#1072;&#1078;&#1085;&#1086; &#1076;&#1083;&#1103; &#1089;&#1077;&#1084;&#1077;&#1081; &#1089; &#1076;&#1077;&#1090;&#1100;&#1084;&#1080;.&lt;/p&gt;&lt;p&gt;&#1041;&#1083;&#1072;&#1075;&#1086;&#1076;&#1072;&#1088;&#1103; &#1073;&#1083;&#1080;&#1079;&#1086;&#1089;&#1090;&#1080; &#1082; &#1075;&#1083;&#1072;&#1074;&#1085;&#1099;&#1084; &#1090;&#1088;&#1072;&#1085;&#1089;&#1087;&#1086;&#1088;&#1090;&#1085;&#1099;&#1084; &#1091;&#1079;&#1083;&#1072;&#1084;, &lt;strong&gt;Castle View&lt;/strong&gt; &#1086;&#1073;&#1077;&#1089;&#1087;&#1077;&#1095;&#1080;&#1074;&#1072;&#1077;&#1090; &#1083;&#1077;&#1075;&#1082;&#1080;&#1081; &#1076;&#1086;&#1089;&#1090;&#1091;&#1087; &#1082; &#1088;&#1072;&#1079;&#1083;&#1080;&#1095;&#1085;&#1099;&#1084; &#1095;&#1072;&#1089;&#1090;&#1103;&#1084; &#1075;&#1086;&#1088;&#1086;&#1076;&#1072;. &#1042; &#1087;&#1077;&#1096;&#1077;&#1081; &#1076;&#1086;&#1089;&#1090;&#1091;&#1087;&#1085;&#1086;&#1089;&#1090;&#1080; &#1085;&#1072;&#1093;&#1086;&#1076;&#1103;&#1090;&#1089;&#1103; &#1072;&#1074;&#1090;&#1086;&#1073;&#1091;&#1089;&#1085;&#1099;&#1077; &#1086;&#1089;&#1090;&#1072;&#1085;&#1086;&#1074;&#1082;&#1080; &#1080; &#1089;&#1090;&#1072;&#1085;&#1094;&#1080;&#1103; &#1084;&#1077;&#1090;&#1088;&#1086;, &#1072; &#1090;&#1072;&#1082;&#1078;&#1077; &lt;strong&gt;&#1078;&#1077;&#1083;&#1077;&#1079;&#1085;&#1086;&#1076;&#1086;&#1088;&#1086;&#1078;&#1085;&#1072;&#1103; &#1089;&#1090;&#1072;&#1085;&#1094;&#1080;&#1103;&lt;/strong&gt; Belgrano C, &#1095;&#1090;&#1086; &#1087;&#1086;&#1079;&#1074;&#1086;&#1083;&#1103;&#1077;&#1090; &#1073;&#1099;&#1089;&#1090;&#1088;&#1086; &#1076;&#1086;&#1073;&#1088;&#1072;&#1090;&#1100;&#1089;&#1103; &#1076;&#1086; &#1076;&#1077;&#1083;&#1086;&#1074;&#1086;&#1075;&#1086; &#1094;&#1077;&#1085;&#1090;&#1088;&#1072; &#1080;&#1083;&#1080; &#1076;&#1088;&#1091;&#1075;&#1080;&#1093; &#1088;&#1072;&#1081;&#1086;&#1085;&#1086;&#1074; &#1041;&#1091;&#1101;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;&#1072;.&lt;/p&gt;&lt;p&gt;&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089; &#1090;&#1072;&#1082;&#1078;&#1077; &#1087;&#1088;&#1077;&#1076;&#1083;&#1072;&#1075;&#1072;&#1077;&#1090; &lt;strong&gt;&#1074;&#1099;&#1089;&#1086;&#1082;&#1080;&#1081; &#1091;&#1088;&#1086;&#1074;&#1077;&#1085;&#1100; &#1073;&#1077;&#1079;&#1086;&#1087;&#1072;&#1089;&#1085;&#1086;&#1089;&#1090;&#1080;&lt;/strong&gt; &#1080; &#1089;&#1086;&#1074;&#1088;&#1077;&#1084;&#1077;&#1085;&#1085;&#1091;&#1102; &#1080;&#1085;&#1092;&#1088;&#1072;&#1089;&#1090;&#1088;&#1091;&#1082;&#1090;&#1091;&#1088;&#1091;: &#1082;&#1088;&#1091;&#1075;&#1083;&#1086;&#1089;&#1091;&#1090;&#1086;&#1095;&#1085;&#1091;&#1102; &#1086;&#1093;&#1088;&#1072;&#1085;&#1091;, &#1087;&#1072;&#1088;&#1082;&#1086;&#1074;&#1082;&#1091;, &#1090;&#1088;&#1077;&#1085;&#1072;&#1078;&#1077;&#1088;&#1085;&#1099;&#1081; &#1079;&#1072;&#1083; &#1080; &#1073;&#1072;&#1089;&#1089;&#1077;&#1081;&#1085;.&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"59 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-1.jpg</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"59 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-9.jpg</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"59 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-7.jpg</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"59 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-3.jpg</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"60 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-10.jpg</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"62 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-11-1.jpg</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"61 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-8-1.jpg</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"61 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-6-1.jpg</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"61 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-4-1.jpg</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"61 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-5-1.jpg</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"60 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-12.jpg</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"59 characters\">properties/gk-castle-view/gk-buenos-aires-castle-view-2.jpg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>video</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8175</span>\"\n  \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Luis Maria Campos 1000</span>\"\n  \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-34.5710205</span>\"\n  \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-58.4373146</span>\"\n  \"<span class=sf-dump-key>number_block</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>number_flat</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>parking</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>year_built</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>build_class</span>\" => \"<span class=sf-dump-str title=\"8 characters\">business</span>\"\n  \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n  \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256279108\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1837401427 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">7474</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://xmetr.gc/admin/real-estate/projects/edit/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6ImJjZmY3MjVlLTg2M2ItNDViZC05OWUwLTI1Zjg0Y2YyNTEwNSIsImMiOjE3NTUwMjcwOTM1OTAsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1755027093$o110$g1$t1755028296$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IitGbUZ0eEMvb3ZSS25OZEpzVGJrcGc9PSIsInZhbHVlIjoiSWViRHdLb3p4aWdwTks2S0JISnYrckFnYTFYS1lNNWczRXBNMTRHdklFd1FJQXlDa3lFTkdXUlAzOFpzMzc4N0JrZ1VHRllYRGdaYWpLRzhzRVNIeFpMZitSUUo4L1RBUU9ETDBvMkVSMUpXbXo4c0tkNEU1WHVYdTY3NjhFZ2YiLCJtYWMiOiI2Nzg2NzBmNzM5MmI5ZjhjOTg3NjMwYWI0YzZhMTEzMDBhNWNhZTkzNmQ1MmE0NDQ3MTdhYjg4YmRmNWVjOTMwIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IlZLbTdWVlB6S25JVTh4WllXUUZWZnc9PSIsInZhbHVlIjoiYlBPS0M4empjWkdGSVAvRk9uWS9DbFFNZUZpQW5GS2lKNWVzaTI3UmUxa3diYmRxakRwMzFDOEpJcm1OMi9NSTFjYlZTMzlJYk1LRXozaUFwQXJLQXpCQ2V6Sy9TekYzYm4rYUY4WDJycC8yQUZmZzdFSER3RnNzR1Zyc24rQ2UiLCJtYWMiOiIzMDY4YWJhYjAxMDI0ZTExZGI4OTAxZGIwOWE0ZGE3MGFkZGVhMmVlYjc3YTIxN2M1M2NhYTY3NzZlZGYyZjkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837401427\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-92640913 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h7bKZg3z6MqqFVW5H8LdcPXPIAeeQnbnWZpFJ4ZR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92640913\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1797566443 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 19:54:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://xmetr.gc/admin/real-estate/projects/edit/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797566443\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-695100615 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">https://xmetr.gc/admin/real-estate/projects/edit/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695100615\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/projects/edit/25", "action_name": "project.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\ProjectController@update"}, "badge": "302 Found"}}