{"__meta": {"id": "01K2FX52GC9S7816ZS576RB268", "datetime": "2025-08-12 19:47:12", "utime": **********.013487, "method": "GET", "uri": "/en/ajax/properties/count?_token=rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1&features%5B0%5D=2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.229425, "end": **********.013513, "duration": 0.784088134765625, "duration_str": "784ms", "measures": [{"label": "Booting", "start": **********.229425, "relative_start": 0, "end": **********.935561, "relative_end": **********.935561, "duration": 0.****************, "duration_str": "706ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.935573, "relative_start": 0.****************, "end": **********.013517, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "77.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.951789, "relative_start": 0.****************, "end": **********.968099, "relative_end": **********.968099, "duration": 0.*****************, "duration_str": "16.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.009091, "relative_start": 0.****************, "end": **********.010756, "relative_end": **********.010756, "duration": 0.0016651153564453125, "duration_str": "1.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01421, "accumulated_duration_str": "14.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs_translations` where `lang_code` = 'en' and `key` = 'properties' limit 1", "type": "query", "params": [], "bindings": ["en", "properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.97814, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:98", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:98", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "98"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 2.393}, {"sql": "select * from `slugs` where `key` = 'properties' limit 1", "type": "query", "params": [], "bindings": ["properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9800801, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:107", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:107", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "107"}, "connection": "xmetr", "explain": null, "start_percent": 2.393, "width_percent": 2.393}, {"sql": "select `re_properties`.`id` from `re_properties` inner join `re_property_features` on `re_properties`.`id` = `re_property_features`.`property_id` where `re_property_features`.`feature_id` in ('2') group by `re_properties`.`id` having COUNT(DISTINCT re_property_features.feature_id) = 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 854}, {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 553}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PropertyFilterController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PropertyFilterController.php", "line": 106}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9925158, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "PropertyRepository.php:854", "source": {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 854}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FRepositories%2FEloquent%2FPropertyRepository.php:854", "ajax": false, "filename": "PropertyRepository.php", "line": "854"}, "connection": "xmetr", "explain": null, "start_percent": 4.785, "width_percent": 26.531}, {"sql": "select count(*) as aggregate from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-08-12 19:47:11' or `never_expired` = 1) and `status` != 'rented' and `id` in (72, 73, 77, 78, 79, 80, 83, 84, 85, 86, 87, 88, 89, 90, 91, 94, 96, 97, 98, 102, 113, 115, 125, 126, 127, 128, 129, 130, 131, 134, 135, 136, 138, 139, 140, 141, 142, 144, 147, 149, 150, 151, 153, 154, 155, 157, 158, 169, 183, 184, 186, 188, 191, 193, 195, 196, 199, 201, 202, 203, 204, 211, 213, 214, 218, 219, 222, 227, 228, 229, 230, 231, 232, 233, 234, 235, 248, 249, 250, 255, 256, 257, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 305, 306, 308, 309, 310, 311, 313, 314, 316, 321, 328, 331, 336, 337, 338, 339, 341, 342, 343, 344, 345, 347, 349, 350, 355, 356, 360, 363, 365, 366, 367, 368, 372, 374, 377, 379, 382, 383, 385, 386, 387, 388, 389, 393, 394, 397, 398, 400, 401, 404, 405, 406, 409, 413, 420, 423, 430, 434, 435, 441, 443, 448, 451, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 482, 487, 488, 489, 490, 491, 494, 499, 500, 501, 506, 508, 509, 510, 511, 514, 517, 518, 519, 520, 521, 522, 531, 533, 534, 536, 537, 538, 542, 544, 558, 561, 566, 567, 569, 570, 572, 574, 575, 580, 581, 583, 584, 585, 587, 595, 596, 597, 598, 600, 601, 602, 606, 609, 610, 614, 616, 621, 622, 623, 624, 625, 626, 627, 630, 632, 633, 634, 638, 639, 640, 641, 642, 645, 646, 648, 658, 662, 664, 665, 666, 667, 670, 673, 674, 675, 676, 683, 686, 688, 689, 693, 702, 706, 708, 710, 711, 716, 717, 719, 720, 724, 726, 727, 736, 745, 746, 749, 752, 755, 756, 759, 764, 767, 768, 773, 784, 785, 786, 787, 788, 794, 795, 796, 797, 800, 807, 820, 822, 823, 824, 825, 826, 827, 828, 836, 837, 838, 839, 840, 841, 842, 843, 857, 859, 860, 861, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 875, 881, 890, 898, 899, 900, 901, 902, 903, 905, 906, 907, 913, 914, 916, 919, 921, 924, 925, 926, 929, 930, 935, 937, 940, 941, 943, 944, 949, 950, 951, 952, 955, 956, 957, 958, 963, 967, 968, 972, 973, 975, 982, 983, 985, 992, 994, 996, 998, 1000, 1002, 1008, 1009, 1010, 1011, 1013, 1014, 1015, 1016, 1018, 1023, 1033, 1034, 1036, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1047, 1050, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1065, 1066, 1067, 1070, 1076, 1077, 1078, 1079, 1080, 1081, 1084, 1085, 1086, 1087, 1088, 1100, 1101, 1108, 1109, 1110, 1111, 1112, 1125, 1130, 1131, 1133, 1136, 1140, 1145, 1147, 1148, 1149, 1150, 1151, 1152, 1153, 1156, 1157, 1161, 1162, 1163, 1164, 1166, 1173, 1174, 1175, 1176, 1182, 1183, 1184, 1185, 1186, 1188, 1189, 1190, 1191, 1195, 1196, 1197, 1200, 1203, 1205, 1208, 1209, 1212, 1216, 1219, 1220, 1221, 1222, 1223, 1227, 1228)", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-08-12 19:47:11", 1, "rented", 72, 73, 77, 78, 79, 80, 83, 84, 85, 86, 87, 88, 89, 90, 91, 94, 96, 97, 98, 102, 113, 115, 125, 126, 127, 128, 129, 130, 131, 134, 135, 136, 138, 139, 140, 141, 142, 144, 147, 149, 150, 151, 153, 154, 155, 157, 158, 169, 183, 184, 186, 188, 191, 193, 195, 196, 199, 201, 202, 203, 204, 211, 213, 214, 218, 219, 222, 227, 228, 229, 230, 231, 232, 233, 234, 235, 248, 249, 250, 255, 256, 257, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 305, 306, 308, 309, 310, 311, 313, 314, 316, 321, 328, 331, 336, 337, 338, 339, 341, 342, 343, 344, 345, 347, 349, 350, 355, 356, 360, 363, 365, 366, 367, 368, 372, 374, 377, 379, 382, 383, 385, 386, 387, 388, 389, 393, 394, 397, 398, 400, 401, 404, 405, 406, 409, 413, 420, 423, 430, 434, 435, 441, 443, 448, 451, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 482, 487, 488, 489, 490, 491, 494, 499, 500, 501, 506, 508, 509, 510, 511, 514, 517, 518, 519, 520, 521, 522, 531, 533, 534, 536, 537, 538, 542, 544, 558, 561, 566, 567, 569, 570, 572, 574, 575, 580, 581, 583, 584, 585, 587, 595, 596, 597, 598, 600, 601, 602, 606, 609, 610, 614, 616, 621, 622, 623, 624, 625, 626, 627, 630, 632, 633, 634, 638, 639, 640, 641, 642, 645, 646, 648, 658, 662, 664, 665, 666, 667, 670, 673, 674, 675, 676, 683, 686, 688, 689, 693, 702, 706, 708, 710, 711, 716, 717, 719, 720, 724, 726, 727, 736, 745, 746, 749, 752, 755, 756, 759, 764, 767, 768, 773, 784, 785, 786, 787, 788, 794, 795, 796, 797, 800, 807, 820, 822, 823, 824, 825, 826, 827, 828, 836, 837, 838, 839, 840, 841, 842, 843, 857, 859, 860, 861, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 875, 881, 890, 898, 899, 900, 901, 902, 903, 905, 906, 907, 913, 914, 916, 919, 921, 924, 925, 926, 929, 930, 935, 937, 940, 941, 943, 944, 949, 950, 951, 952, 955, 956, 957, 958, 963, 967, 968, 972, 973, 975, 982, 983, 985, 992, 994, 996, 998, 1000, 1002, 1008, 1009, 1010, 1011, 1013, 1014, 1015, 1016, 1018, 1023, 1033, 1034, 1036, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1047, 1050, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1065, 1066, 1067, 1070, 1076, 1077, 1078, 1079, 1080, 1081, 1084, 1085, 1086, 1087, 1088, 1100, 1101, 1108, 1109, 1110, 1111, 1112, 1125, 1130, 1131, 1133, 1136, 1140, 1145, 1147, 1148, 1149, 1150, 1151, 1152, 1153, 1156, 1157, 1161, 1162, 1163, 1164, 1166, 1173, 1174, 1175, 1176, 1182, 1183, 1184, 1185, 1186, 1188, 1189, 1190, 1191, 1195, 1196, 1197, 1200, 1203, 1205, 1208, 1209, 1212, 1216, 1219, 1220, 1221, 1222, 1223, 1227, 1228], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 559}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PropertyFilterController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PropertyFilterController.php", "line": 106}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.998099, "duration": 0.00976, "duration_str": "9.76ms", "memory": 0, "memory_str": null, "filename": "PropertyRepository.php:559", "source": {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 559}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FRepositories%2FEloquent%2FPropertyRepository.php:559", "ajax": false, "filename": "PropertyRepository.php", "line": "559"}, "connection": "xmetr", "explain": null, "start_percent": 31.316, "width_percent": 68.684}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/ajax/properties/count?_token=rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1&features%5...", "action_name": "public.ajax.properties.count", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PropertyFilterController@getPropertiesCount", "uri": "GET en/ajax/properties/count", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PropertyFilterController@getPropertiesCount<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FPropertyFilterController.php:17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts", "prefix": "/en", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FPropertyFilterController.php:17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/Fronts/PropertyFilterController.php:17-111</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, Xmetr\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware", "duration": "783ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2020837166 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>2</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020837166\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1495517706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1495517706\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-764145375 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6ImJjZmY3MjVlLTg2M2ItNDViZC05OWUwLTI1Zjg0Y2YyNTEwNSIsImMiOjE3NTUwMjcwOTM1OTAsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1755027093$o110$g1$t1755028009$j47$l0$h0; XSRF-TOKEN=eyJpdiI6ImhRUWtBSGV3MWc1YlZwOVZEN0hkM0E9PSIsInZhbHVlIjoiQWJ2cU9DMFZLcncwTGl6Z1pvSzlrNGtIZDYyT2Q3L2N0ZlZTa2hHZUxIcFUvY1A0TFBiMFVKci8relBjNWVIZUhqL0N0bG9VMG9ZV01qRi9NTVZwZE5sTDN6Tks4bTBTS3ljTkFNbjk4MmM1eUZ6VGxnT2w5UWc4NmJ3cEdCa1kiLCJtYWMiOiI0NjkwM2NiMDc2NTlmMTI2YmMxYTM0ODA2NDk4YTVmYzA4YTU5NWE0OWRjYmQzMzJjMTc3Y2E3ODgxZTU2NzIxIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IlhXclliWWhjc1M2Q1VYNGFoVTNleGc9PSIsInZhbHVlIjoiNDkxaXRJSTlSeGNSL25rbWVHZC9KUTREUElMRzVXNU8zeGdyUXFycmpNcnMrRVkzUXpYRm1ZVDNaRStqSVpHQWMyOFY0YW4xQ3N3TE9WSDBkallJK0t5RStXeEVMQTROMFBEaEdQc1pBOS9NWnRISkNJeDJoNVZRRnNTdXFub1giLCJtYWMiOiI4MDhjZDllMGRkY2U2NTlhNWY1MmJhNzczMDQzYjc1ZDg5OWNkYThkYmQ4OTVkYWE1YTcwN2Q5NGIxYjQ4NWU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764145375\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-432207797 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h7bKZg3z6MqqFVW5H8LdcPXPIAeeQnbnWZpFJ4ZR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432207797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1163242805 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 19:47:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163242805\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-726954564 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K2FX4ETB8M2PGSY429MPHDKD</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01K2FX4XZ1W4C2XN4GGVZ979FG</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01K2FX501K1REXGNSBSFR18W55</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726954564\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/ajax/properties/count?_token=rvmU18cXq9vOggd1WnlIFUNfrsWGc8cda4pLNLH1&features%5...", "action_name": "public.ajax.properties.count", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PropertyFilterController@getPropertiesCount"}, "badge": null}}